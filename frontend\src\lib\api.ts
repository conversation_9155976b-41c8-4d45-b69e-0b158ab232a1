// API Client for BitsTech POS System

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5001/api'
const USE_REAL_DATABASE = true // Use real database for entire system
const DISABLE_FALLBACK_MODE = false // Enable fallback mode when backend is not available
const FORCE_REAL_DATABASE_ONLY = false // Allow fallback mode for better user experience

// Types
interface ApiResponse<T = any> {
  success: boolean
  data?: T
  error?: string
  message?: string
  token?: string
}

interface LoginRequest {
  email: string
  password: string
}

interface RegisterRequest {
  firstName: string
  lastName: string
  username: string
  email: string
  password: string
}

interface User {
  id: string
  username: string
  email: string
  firstName: string
  lastName: string
  fullName: string
  role: string
  isActive: boolean
  preferences: {
    language: string
    currency: string
    theme: string
  }
  lastLogin?: string
}

interface AuthResponse {
  token: string
  data: User
}

// API Client Class
class ApiClient {
  private baseURL: string
  private token: string | null = null

  constructor(baseURL: string = API_BASE_URL) {
    this.baseURL = baseURL
    this.token = this.getStoredToken()

    // Debug information
    console.log('🔧 API Client initialized:', {
      baseURL: this.baseURL,
      hasToken: !!this.token,
      environment: process.env.NODE_ENV
    })
  }

  // Token management
  private getStoredToken(): string | null {
    if (typeof window !== 'undefined') {
      return localStorage.getItem('bitstech_auth_token')
    }
    return null
  }

  private setStoredToken(token: string): void {
    if (typeof window !== 'undefined') {
      localStorage.setItem('bitstech_auth_token', token)
    }
    this.token = token
  }

  private removeStoredToken(): void {
    if (typeof window !== 'undefined') {
      localStorage.removeItem('bitstech_auth_token')
    }
    this.token = null
  }

  // Check if user is authenticated
  isAuthenticated(): boolean {
    return !!this.token
  }

  // HTTP request helper with fallback mechanism
  private async request<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<ApiResponse<T>> {
    const url = `${this.baseURL}${endpoint}`

    const config: RequestInit = {
      headers: {
        'Content-Type': 'application/json',
        ...(this.token && { Authorization: `Bearer ${this.token}` }),
        ...options.headers,
      },
      ...options,
    }

    try {
      // Add timeout to fetch request
      const controller = new AbortController()
      const timeoutId = setTimeout(() => controller.abort(), 5000)

      const response = await fetch(url, {
        ...config,
        signal: controller.signal
      })

      clearTimeout(timeoutId)

      if (!response.ok) {
        let errorMessage = `HTTP error! status: ${response.status}`
        try {
          const errorData = await response.json()
          errorMessage = errorData.error || errorData.message || errorMessage
        } catch {
          // If parsing fails, use default message
        }
        throw new Error(errorMessage)
      }

      const data = await response.json()
      return data
    } catch (error: any) {
      // Check if it's a network error (server not available)
      const isNetworkError = error.name === 'AbortError' ||
                            error.message.includes('fetch') ||
                            error.message.includes('Failed to fetch') ||
                            error.message.includes('NetworkError') ||
                            error.message.includes('ERR_CONNECTION_REFUSED')

      if (isNetworkError && DISABLE_FALLBACK_MODE) {
        console.error(`❌ Backend server not available for ${endpoint}. Real database mode requires backend connection.`)
        console.error(`🔧 Please start the backend server: cd backend && node working-server.js`)
        console.error(`🔧 Backend should be running on: http://localhost:5001`)
        console.error(`🔧 Check if MongoDB is running: Get-Service -Name "*mongo*" (Windows) or brew services list | grep mongo (Mac)`)

        // Return a more user-friendly error response instead of throwing
        return {
          success: false,
          error: `Backend server not available. Please start the backend server at http://localhost:5001`,
          message: 'Backend connection required for full functionality',
          offline: true
        } as ApiResponse<T>
      } else if (isNetworkError && !DISABLE_FALLBACK_MODE) {
        console.warn(`🔌 Backend server not available for ${endpoint}. Using fallback mode.`)
        // Return appropriate fallback response based on endpoint
        return await this.getFallbackResponse<T>(endpoint, options.method || 'GET')
      }

      // Log errors with more context
      console.warn('API request failed:', {
        endpoint,
        url,
        method: config.method || 'GET',
        error: error.message,
        baseURL: this.baseURL
      })

      return {
        success: false,
        error: error.message || 'Network error'
      }
    }
  }

  // Fallback response generator for when backend is not available
  private async getFallbackResponse<T>(endpoint: string, method: string): Promise<ApiResponse<T>> {
    console.log(`📦 Generating fallback response for ${method} ${endpoint}`)

    // Products endpoints
    if (endpoint.includes('/products')) {
      if (method === 'GET') {
        return {
          success: true,
          data: [] as T,
          message: 'Backend not available - using empty data'
        }
      }
      return { success: true, data: { id: 'temp_' + Date.now() } as T }
    }

    // Categories endpoints
    if (endpoint.includes('/categories')) {
      if (method === 'GET') {
        return {
          success: true,
          data: [] as T,
          message: 'Backend not available - using empty data'
        }
      }
      return { success: true, data: { id: 'temp_' + Date.now() } as T }
    }

    // Cart endpoints
    if (endpoint.includes('/cart')) {
      if (method === 'GET') {
        return {
          success: true,
          data: { items: [] } as T,
          message: 'Backend not available - using empty cart'
        }
      }
      return { success: true, data: {} as T }
    }

    // Settings endpoints
    if (endpoint.includes('/settings')) {
      if (method === 'GET') {
        return {
          success: true,
          data: {
            paymentMethods: {},
            currency: 'MMK',
            language: 'en',
            theme: 'light'
          } as T,
          message: 'Backend not available - using default settings'
        }
      }
      return { success: true, data: {} as T }
    }

    // Sales endpoints
    if (endpoint.includes('/sales')) {
      if (method === 'GET') {
        return {
          success: true,
          data: [] as T,
          message: 'Backend not available - using empty sales data'
        }
      }
      if (method === 'POST') {
        return {
          success: true,
          data: {
            _id: 'offline_sale_' + Date.now(),
            saleId: 'offline_sale_' + Date.now(),
            saleNumber: 'INV-' + new Date().getFullYear() + '-' + String(Date.now()).slice(-6)
          } as T,
          message: 'Backend not available - sale saved locally'
        }
      }
      return { success: true, data: {} as T }
    }

    // Checkout endpoints
    if (endpoint.includes('/checkout')) {
      return {
        success: true,
        data: {
          saleId: 'offline_checkout_' + Date.now(),
          saleNumber: 'INV-' + new Date().getFullYear() + '-' + String(Date.now()).slice(-6)
        } as T,
        message: 'Backend not available - checkout processed locally'
      }
    }

    // Health check endpoints - Try real API first
    if (endpoint.includes('/auth/health') || endpoint.includes('/health')) {
      try {
        const healthUrl = endpoint.includes('/auth/health') ? '/health' : endpoint
        const response = await fetch(`${this.baseURL.replace('/api', '')}${healthUrl}`, {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
          },
        })

        if (response.ok) {
          const data = await response.json()
          return {
            success: true,
            data: data
          }
        }
      } catch (error) {
        console.warn('Health check failed, using fallback:', error)
      }

      return {
        success: true,
        data: {
          status: 'offline',
          message: 'Backend server not available'
        } as T,
        message: 'Backend not available - health check failed'
      }
    }

    // Authentication endpoints
    if (endpoint.includes('/auth/login')) {
      return {
        success: true,
        data: {
          token: 'offline_token_' + Date.now(),
          user: {
            id: 'offline_user',
            username: 'offline_user',
            email: '<EMAIL>',
            firstName: 'Offline',
            lastName: 'User',
            fullName: 'Offline User',
            role: 'admin',
            isActive: true,
            preferences: {
              language: 'en',
              currency: 'MMK',
              theme: 'light'
            }
          }
        } as T,
        message: 'Backend not available - using offline authentication'
      }
    }

    if (endpoint.includes('/auth/register')) {
      return {
        success: true,
        data: {
          token: 'offline_token_' + Date.now(),
          user: {
            id: 'offline_user_' + Date.now(),
            username: 'new_offline_user',
            email: '<EMAIL>',
            firstName: 'New',
            lastName: 'User',
            fullName: 'New User',
            role: 'user',
            isActive: true,
            preferences: {
              language: 'en',
              currency: 'MMK',
              theme: 'light'
            }
          }
        } as T,
        message: 'Backend not available - using offline registration'
      }
    }

    if (endpoint.includes('/auth/me')) {
      return {
        success: true,
        data: {
          id: 'offline_user',
          username: 'offline_user',
          email: '<EMAIL>',
          firstName: 'Offline',
          lastName: 'User',
          fullName: 'Offline User',
          role: 'admin',
          isActive: true,
          preferences: {
            language: 'en',
            currency: 'MMK',
            theme: 'light'
          }
        } as T,
        message: 'Backend not available - using offline user data'
      }
    }

    // Default fallback
    return {
      success: true,
      data: {} as T,
      message: 'Backend not available - using fallback mode'
    }
  }

  // Authentication methods
  async login(credentials: LoginRequest): Promise<AuthResponse> {
    try {
      const response = await this.request<any>('/auth/login', {
        method: 'POST',
        body: JSON.stringify(credentials),
      })

      if (response.success && response.data) {
        // Handle both direct response and nested response formats
        const authData = response.data.token ? response.data : response
        const token = authData.token || response.token
        const userData = authData.data || authData.user || response.data

        if (token && userData) {
          this.setStoredToken(token)
          return {
            token: token,
            data: userData
          }
        }
      }

      // Check if this is a fallback response (backend not available)
      if (response.message?.includes('Backend not available')) {
        console.log('🔌 Using offline authentication mode')

        // For offline mode, accept any credentials and return offline user
        const offlineToken = 'offline_token_' + Date.now()
        const offlineUser = {
          id: 'offline_user',
          username: credentials.email.split('@')[0] || 'offline_user',
          email: credentials.email,
          firstName: 'Offline',
          lastName: 'User',
          fullName: 'Offline User',
          role: 'admin',
          isActive: true,
          preferences: {
            language: 'en',
            currency: 'MMK',
            theme: 'light'
          }
        }

        this.setStoredToken(offlineToken)
        return {
          token: offlineToken,
          data: offlineUser
        }
      }

      throw new Error('Login failed - Invalid response format')
    } catch (error: any) {
      console.error('Login error:', error.message)

      // Check if it's a network error
      if (error.message?.includes('Backend server not available') || error.message?.includes('Cannot connect')) {
        throw new Error('Cannot connect to server. Please check if the backend is running.')
      }

      throw new Error(error.message || 'Login failed')
    }
  }

  async register(userData: RegisterRequest): Promise<AuthResponse> {
    try {
      const response = await this.request<any>('/auth/register', {
        method: 'POST',
        body: JSON.stringify(userData),
      })

      if (response.success && response.data) {
        // Handle both direct response and nested response formats
        const authData = response.data.token ? response.data : response
        const token = authData.token || response.token
        const userInfo = authData.data || authData.user || response.data

        if (token && userInfo) {
          this.setStoredToken(token)
          return {
            token: token,
            data: userInfo
          }
        }
      }

      // Check if this is a fallback response (backend not available)
      if (response.message?.includes('Backend not available')) {
        console.log('🔌 Using offline registration mode')

        // For offline mode, accept registration and return offline user
        const offlineToken = 'offline_token_' + Date.now()
        const offlineUser = {
          id: 'offline_user_' + Date.now(),
          username: userData.username,
          email: userData.email,
          firstName: userData.firstName,
          lastName: userData.lastName,
          fullName: `${userData.firstName} ${userData.lastName}`,
          role: 'user',
          isActive: true,
          preferences: {
            language: 'en',
            currency: 'MMK',
            theme: 'light'
          }
        }

        this.setStoredToken(offlineToken)
        return {
          token: offlineToken,
          data: offlineUser
        }
      }

      throw new Error('Registration failed - Unable to create account')
    } catch (error: any) {
      console.error('Registration error:', error.message)
      throw new Error(error.message || 'Registration failed')
    }
  }

  async logout(): Promise<void> {
    try {
      await this.request('/auth/logout', {
        method: 'POST',
      })
    } catch (error) {
      console.error('Logout request failed:', error)
    } finally {
      this.removeStoredToken()
    }
  }

  async getMe(): Promise<User> {
    try {
      const response = await this.request<User>('/auth/me')

      if (response.success !== false && response.data) {
        return response.data
      }

      // Check if this is a fallback response (backend not available)
      if (response.message?.includes('Backend not available')) {
        console.log('🔌 Using offline user data')

        // Return offline user data
        return {
          id: 'offline_user',
          username: 'offline_user',
          email: '<EMAIL>',
          firstName: 'Offline',
          lastName: 'User',
          fullName: 'Offline User',
          role: 'admin',
          isActive: true,
          preferences: {
            language: 'en',
            currency: 'MMK',
            theme: 'light'
          }
        }
      }

      throw new Error('Failed to get user data')
    } catch (error: any) {
      console.error('Get user data error:', error.message)
      throw new Error(error.message || 'Failed to get user data')
    }
  }

  async updateProfile(profileData: Partial<User>): Promise<User> {
    const response = await this.request<User>('/auth/profile', {
      method: 'PUT',
      body: JSON.stringify(profileData),
    })

    if (response.success !== false && response.data) {
      return response.data
    }

    throw new Error('Failed to update profile')
  }

  // Utility methods
  isAuthenticated(): boolean {
    return !!this.token
  }

  getToken(): string | null {
    return this.token
  }

  // Generic HTTP methods
  async get(endpoint: string, params?: any): Promise<any> {
    const queryParams = new URLSearchParams()

    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          queryParams.append(key, value.toString())
        }
      })
    }

    const url = endpoint + (queryParams.toString() ? `?${queryParams.toString()}` : '')
    return this.request(url)
  }

  async post(endpoint: string, data?: any): Promise<any> {
    return this.request(endpoint, {
      method: 'POST',
      body: data ? JSON.stringify(data) : undefined
    })
  }

  async put(endpoint: string, data?: any): Promise<any> {
    return this.request(endpoint, {
      method: 'PUT',
      body: data ? JSON.stringify(data) : undefined
    })
  }

  async delete(endpoint: string): Promise<any> {
    return this.request(endpoint, {
      method: 'DELETE'
    })
  }

  // Products methods
  async getProducts(params?: {
    category?: string
    isActive?: boolean
    isFeatured?: boolean
    page?: number
    limit?: number
    sort?: string
  }): Promise<any> {
    try {
      const queryParams = new URLSearchParams()
      if (params?.category) queryParams.append('category', params.category)
      if (params?.isActive !== undefined) queryParams.append('isActive', params.isActive.toString())
      if (params?.isFeatured !== undefined) queryParams.append('isFeatured', params.isFeatured.toString())
      if (params?.page) queryParams.append('page', params.page.toString())
      if (params?.limit) queryParams.append('limit', params.limit.toString())
      if (params?.sort) queryParams.append('sort', params.sort)

      const endpoint = `/products${queryParams.toString() ? `?${queryParams.toString()}` : ''}`
      return this.request(endpoint)
    } catch (error) {
      console.warn('Products API not available:', error)
      return {
        success: true,
        data: [],
        count: 0,
        total: 0
      }
    }
  }

  async getProduct(id: string): Promise<any> {
    return this.request(`/products/${id}`)
  }

  async createProduct(productData: any): Promise<any> {
    return this.request('/products', {
      method: 'POST',
      body: JSON.stringify(productData)
    })
  }

  async updateProduct(id: string, productData: any): Promise<any> {
    return this.request(`/products/${id}`, {
      method: 'PUT',
      body: JSON.stringify(productData)
    })
  }

  async deleteProduct(id: string): Promise<any> {
    return this.request(`/products/${id}`, {
      method: 'DELETE'
    })
  }

  async searchProducts(query: string, filters?: any): Promise<any> {
    const queryParams = new URLSearchParams({ q: query })

    if (filters) {
      Object.entries(filters).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          queryParams.append(key, value.toString())
        }
      })
    }

    return this.request(`/products/search?${queryParams.toString()}`)
  }

  // Categories methods
  async getCategories(params?: {
    parent?: string
    isActive?: boolean
    sort?: string
    page?: number
    limit?: number
  }): Promise<any> {
    const queryParams = new URLSearchParams()

    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined) {
          queryParams.append(key, value.toString())
        }
      })
    }

    const endpoint = `/categories${queryParams.toString() ? `?${queryParams.toString()}` : ''}`
    console.log('🔄 Fetching categories from:', `${this.baseURL}${endpoint}`)

    const response = await this.request(endpoint)
    console.log('📂 Categories API response:', response)

    return response
  }

  async getCategory(id: string): Promise<any> {
    return this.request(`/categories/${id}`)
  }

  async createCategory(categoryData: any): Promise<any> {
    try {
      console.log('API: Creating category with data:', categoryData)

      const response = await this.request('/categories', {
        method: 'POST',
        body: JSON.stringify(categoryData)
      })

      console.log('API: Category creation response:', response)
      return response
    } catch (error) {
      console.error('API: Category creation error:', error)
      throw error
    }
  }

  async updateCategory(id: string, categoryData: any): Promise<any> {
    return this.request(`/categories/${id}`, {
      method: 'PUT',
      body: JSON.stringify(categoryData)
    })
  }

  async deleteCategory(id: string): Promise<any> {
    return this.request(`/categories/${id}`, {
      method: 'DELETE'
    })
  }

  // Sales methods
  async getSales(params?: any): Promise<any> {
    try {
      const queryParams = new URLSearchParams()

      if (params) {
        Object.entries(params).forEach(([key, value]) => {
          if (value !== undefined && value !== null) {
            queryParams.append(key, value.toString())
          }
        })
      }

      const endpoint = `/sales${queryParams.toString() ? `?${queryParams.toString()}` : ''}`
      return this.request(endpoint)
    } catch (error) {
      console.warn('Sales API not available:', error)
      return {
        success: true,
        data: [],
        count: 0
      }
    }
  }

  async getSale(id: string): Promise<any> {
    return this.request(`/sales/${id}`)
  }

  async createSale(saleData: any): Promise<any> {
    try {
      return this.request('/sales', {
        method: 'POST',
        body: JSON.stringify(saleData)
      })
    } catch (error) {
      console.warn('Sales API not available:', error)
      return {
        success: false,
        error: 'Sales API not available'
      }
    }
  }

  async getSalesStats(period?: string): Promise<any> {
    try {
      const queryParams = new URLSearchParams()

      if (period) {
        queryParams.append('period', period)
      }

      const endpoint = `/sales/stats${queryParams.toString() ? `?${queryParams.toString()}` : ''}`
      return this.request(endpoint)
    } catch (error) {
      console.warn('Sales stats API not available:', error)
      return {
        success: true,
        data: {
          totalSales: 0,
          totalRevenue: 0,
          totalTransactions: 0,
          averageOrderValue: 0,
          period: period || '30d'
        }
      }
    }
  }

  // Suppliers methods - removed duplicate, keeping the one below

  // Purchase Orders methods - removed duplicate, keeping the one below

  // Stock Adjustments methods
  async getStockAdjustments(params?: any): Promise<any> {
    try {
      const queryParams = new URLSearchParams()

      if (params) {
        Object.entries(params).forEach(([key, value]) => {
          if (value !== undefined && value !== null) {
            queryParams.append(key, value.toString())
          }
        })
      }

      const endpoint = `/stock-adjustments${queryParams.toString() ? `?${queryParams.toString()}` : ''}`
      return this.request(endpoint)
    } catch (error) {
      console.warn('Stock adjustments API not available:', error)
      return {
        success: true,
        data: [],
        count: 0
      }
    }
  }

  async getStockAdjustment(id: string): Promise<any> {
    return this.request(`/stock-adjustments/${id}`)
  }

  async createStockAdjustment(adjustmentData: any): Promise<any> {
    return this.request('/stock-adjustments', {
      method: 'POST',
      body: JSON.stringify(adjustmentData)
    })
  }

  async updateStockAdjustment(id: string, adjustmentData: any): Promise<any> {
    return this.request(`/stock-adjustments/${id}`, {
      method: 'PUT',
      body: JSON.stringify(adjustmentData)
    })
  }

  async deleteStockAdjustment(id: string): Promise<any> {
    return this.request(`/stock-adjustments/${id}`, {
      method: 'DELETE'
    })
  }

  // ==========================================
  // CUSTOMERS
  // ==========================================
  async getCustomers(params?: {
    page?: number
    limit?: number
    search?: string
    isActive?: boolean
  }): Promise<any> {
    try {
      const queryParams = new URLSearchParams()
      if (params?.page) queryParams.append('page', params.page.toString())
      if (params?.limit) queryParams.append('limit', params.limit.toString())
      if (params?.search) queryParams.append('search', params.search)
      if (params?.isActive !== undefined) queryParams.append('isActive', params.isActive.toString())

      return this.request(`/customers?${queryParams}`)
    } catch (error) {
      console.warn('Customers API not available:', error)
      return {
        success: true,
        data: [],
        count: 0
      }
    }
  }

  async getCustomer(id: string): Promise<any> {
    return this.request(`/customers/${id}`)
  }

  async createCustomer(customerData: any): Promise<any> {
    return this.request('/customers', {
      method: 'POST',
      body: JSON.stringify(customerData)
    })
  }

  async updateCustomer(id: string, customerData: any): Promise<any> {
    return this.request(`/customers/${id}`, {
      method: 'PUT',
      body: JSON.stringify(customerData)
    })
  }

  async deleteCustomer(id: string): Promise<any> {
    return this.request(`/customers/${id}`, {
      method: 'DELETE'
    })
  }

  // ==========================================
  // POS METHODS
  // ==========================================

  // Cart management
  async getCart(): Promise<any> {
    try {
      return this.request('/pos/cart')
    } catch (error) {
      console.warn('Cart API not available, returning empty cart:', error)
      return {
        success: true,
        data: {
          items: [],
          total: 0
        },
        message: 'Using local cart'
      }
    }
  }

  async addToCart(productId: string, quantity: number = 1): Promise<any> {
    return this.request('/pos/cart/add', {
      method: 'POST',
      body: JSON.stringify({ productId, quantity })
    })
  }

  async updateCartItem(productId: string, quantity: number): Promise<any> {
    return this.request('/pos/cart/update', {
      method: 'PUT',
      body: JSON.stringify({ productId, quantity })
    })
  }

  async removeFromCart(productId: string): Promise<any> {
    return this.request(`/pos/cart/remove/${productId}`, {
      method: 'DELETE'
    })
  }

  async clearCart(): Promise<any> {
    return this.request('/pos/cart/clear', {
      method: 'DELETE'
    })
  }

  // Checkout
  async validateCheckout(checkoutData: any): Promise<any> {
    return this.request('/pos/checkout/validate', {
      method: 'POST',
      body: JSON.stringify(checkoutData)
    })
  }

  async checkout(checkoutData: any): Promise<any> {
    return this.request('/pos/checkout', {
      method: 'POST',
      body: JSON.stringify(checkoutData)
    })
  }

  async getReceipt(saleId: string): Promise<any> {
    return this.request(`/pos/receipt/${saleId}`)
  }

  // ==========================================
  // DASHBOARD
  // ==========================================

  async getDashboardStats(): Promise<any> {
    try {
      const response = await this.request('/dashboard/stats')
      return response
    } catch (error) {
      console.warn('Dashboard API not available, using fallback data:', error)

      // Return fallback mock data
      const fallbackData = {
        todaySales: { amount: 0, change: 0, transactions: 0 },
        totalProducts: { count: 0, change: 0 },
        lowStockItems: { count: 0, items: [] },
        recentSales: [],
        topProducts: [],
        salesTrend: []
      }

      return {
        success: true,
        data: fallbackData
      }
    }
  }

  async getInventoryAlerts(): Promise<any> {
    try {
      const response = await this.request('/dashboard/inventory-alerts')
      return response
    } catch (error) {
      console.warn('Inventory alerts API not available, using fallback data:', error)

      // Return fallback empty alerts
      return {
        success: true,
        data: {
          lowStock: [],
          outOfStock: []
        }
      }
    }
  }

  // ==========================================
  // REPORTS
  // ==========================================
  async getSalesReport(params: {
    startDate: string
    endDate: string
    groupBy?: 'day' | 'week' | 'month'
  }): Promise<any> {
    if (FORCE_REAL_DATABASE_ONLY) {
      // Force real database only - no fallback
      const queryParams = new URLSearchParams()
      queryParams.append('startDate', params.startDate)
      queryParams.append('endDate', params.endDate)
      if (params.groupBy) queryParams.append('groupBy', params.groupBy)

      return this.request(`/reports/sales?${queryParams}`)
    }

    try {
      const queryParams = new URLSearchParams()
      queryParams.append('startDate', params.startDate)
      queryParams.append('endDate', params.endDate)
      if (params.groupBy) queryParams.append('groupBy', params.groupBy)

      return this.request(`/reports/sales?${queryParams}`)
    } catch (error) {
      console.error('Sales report API failed:', error)
      throw error // Don't fallback to mock data
    }
  }

  async getInventoryReport(): Promise<any> {
    try {
      return this.request('/reports/inventory')
    } catch (error) {
      console.warn('Inventory report API not available:', error)
      return {
        success: true,
        data: {
          lowStock: [],
          outOfStock: [],
          totalProducts: 0
        }
      }
    }
  }

  async getCustomerReport(params: {
    startDate: string
    endDate: string
  }): Promise<any> {
    try {
      const queryParams = new URLSearchParams()
      queryParams.append('startDate', params.startDate)
      queryParams.append('endDate', params.endDate)

      return this.request(`/reports/customers?${queryParams}`)
    } catch (error) {
      console.warn('Customer report API not available:', error)
      return {
        success: true,
        data: {
          totalCustomers: 0,
          newCustomers: 0,
          topCustomers: []
        }
      }
    }
  }

  // ==========================================
  // DATA MANAGEMENT
  // ==========================================

  async resetAllData(): Promise<any> {
    try {
      return this.request('/admin/reset-data', {
        method: 'POST'
      })
    } catch (error) {
      console.warn('Reset API not available:', error)
      return {
        success: false,
        error: 'Reset API not available'
      }
    }
  }

  async resetDashboardData(): Promise<any> {
    try {
      return this.request('/admin/reset-dashboard', {
        method: 'POST'
      })
    } catch (error) {
      console.warn('Dashboard reset API not available:', error)
      return {
        success: false,
        error: 'Dashboard reset API not available'
      }
    }
  }

  async resetSalesData(): Promise<any> {
    try {
      return this.request('/admin/reset-sales', {
        method: 'POST'
      })
    } catch (error) {
      console.error('Sales reset API not available:', error)
      throw new Error('Backend server required for sales data reset. Please start the backend server.')
    }
  }

  async resetInventoryData(): Promise<any> {
    try {
      return this.request('/admin/reset-inventory', {
        method: 'POST'
      })
    } catch (error) {
      console.error('Inventory reset API not available:', error)
      throw new Error('Backend server required for inventory data reset. Please start the backend server.')
    }
  }

  async resetProductsData(): Promise<any> {
    try {
      return this.request('/admin/reset-products', {
        method: 'POST'
      })
    } catch (error) {
      console.error('Products reset API not available:', error)
      throw new Error('Backend server required for products data reset. Please start the backend server.')
    }
  }

  async resetCustomerData(): Promise<any> {
    try {
      return this.request('/admin/reset-customers', {
        method: 'POST'
      })
    } catch (error) {
      console.error('Customer reset API not available:', error)
      throw new Error('Backend server required for customer data reset. Please start the backend server.')
    }
  }

  async clearResetFlag(): Promise<any> {
    try {
      return this.request('/admin/clear-reset-flag', {
        method: 'POST'
      })
    } catch (error) {
      console.error('Clear reset flag API not available:', error)
      throw new Error('Backend server required for clearing reset flag. Please start the backend server.')
    }
  }

  // Reset specific data types


  async resetPurchaseOrdersData(): Promise<any> {
    try {
      return this.request('/admin/reset-purchase-orders', {
        method: 'POST'
      })
    } catch (error) {
      console.error('Purchase orders reset API not available:', error)
      throw new Error('Backend server required for purchase orders data reset. Please start the backend server.')
    }
  }

  async resetReportsData(): Promise<any> {
    try {
      return this.request('/admin/reset-reports', {
        method: 'POST'
      })
    } catch (error) {
      console.error('Reports reset API not available:', error)
      throw new Error('Backend server required for reports data reset. Please start the backend server.')
    }
  }

  async resetUsersData(): Promise<any> {
    try {
      return this.request('/admin/reset-users', {
        method: 'POST'
      })
    } catch (error) {
      console.error('Users reset API not available:', error)
      throw new Error('Backend server required for users data reset. Please start the backend server.')
    }
  }

  async resetAnalyticsData(): Promise<any> {
    try {
      return this.request('/admin/reset-analytics', {
        method: 'POST'
      })
    } catch (error) {
      console.error('Analytics reset API not available:', error)
      throw new Error('Backend server required for analytics data reset. Please start the backend server.')
    }
  }

  async resetKPIData(): Promise<any> {
    try {
      return this.request('/admin/reset-kpis', {
        method: 'POST'
      })
    } catch (error) {
      console.error('KPI reset API not available:', error)
      throw new Error('Backend server required for KPI data reset. Please start the backend server.')
    }
  }

  // ==========================================
  // FORECASTING
  // ==========================================
  async getSalesForecast(params?: {
    period?: string
    confidence?: number
  }): Promise<any> {
    const queryParams = new URLSearchParams()
    if (params?.period) queryParams.append('period', params.period)
    if (params?.confidence) queryParams.append('confidence', params.confidence.toString())

    const endpoint = `/forecasting/sales${queryParams.toString() ? `?${queryParams.toString()}` : ''}`
    return this.request(endpoint)
  }

  async getProductForecast(params?: {
    limit?: number
    period?: string
  }): Promise<any> {
    const queryParams = new URLSearchParams()
    if (params?.limit) queryParams.append('limit', params.limit.toString())
    if (params?.period) queryParams.append('period', params.period)

    const endpoint = `/forecasting/products${queryParams.toString() ? `?${queryParams.toString()}` : ''}`
    return this.request(endpoint)
  }

  async getMarketTrends(params?: {
    period?: string
  }): Promise<any> {
    const queryParams = new URLSearchParams()
    if (params?.period) queryParams.append('period', params.period)

    const endpoint = `/forecasting/trends${queryParams.toString() ? `?${queryParams.toString()}` : ''}`
    return this.request(endpoint)
  }

  // ==========================================
  // PURCHASE ORDERS
  // ==========================================
  async getPurchaseOrders(params?: {
    status?: string
    supplier?: string
    limit?: number
  }): Promise<any> {
    const queryParams = new URLSearchParams()
    if (params?.status) queryParams.append('status', params.status)
    if (params?.supplier) queryParams.append('supplier', params.supplier)
    if (params?.limit) queryParams.append('limit', params.limit.toString())

    const endpoint = `/purchase-orders${queryParams.toString() ? `?${queryParams.toString()}` : ''}`
    return this.request(endpoint)
  }

  async createPurchaseOrder(data: any): Promise<any> {
    return this.request('/purchase-orders', {
      method: 'POST',
      body: JSON.stringify(data)
    })
  }

  async getPurchaseOrder(id: string): Promise<any> {
    return this.request(`/purchase-orders/${id}`)
  }

  async updatePurchaseOrder(id: string, data: any): Promise<any> {
    return this.request(`/purchase-orders/${id}`, {
      method: 'PUT',
      body: JSON.stringify(data)
    })
  }

  async deletePurchaseOrder(id: string): Promise<any> {
    return this.request(`/purchase-orders/${id}`, {
      method: 'DELETE'
    })
  }

  // ==========================================
  // SUPPLIERS
  // ==========================================
  async getSuppliers(params?: {
    active?: boolean
    limit?: number
  }): Promise<any> {
    const queryParams = new URLSearchParams()
    if (params?.active !== undefined) queryParams.append('active', params.active.toString())
    if (params?.limit) queryParams.append('limit', params.limit.toString())

    const endpoint = `/suppliers${queryParams.toString() ? `?${queryParams.toString()}` : ''}`
    return this.request(endpoint)
  }

  async createSupplier(data: any): Promise<any> {
    return this.request('/suppliers', {
      method: 'POST',
      body: JSON.stringify(data)
    })
  }

  async getSupplier(id: string): Promise<any> {
    return this.request(`/suppliers/${id}`)
  }

  async updateSupplier(id: string, data: any): Promise<any> {
    return this.request(`/suppliers/${id}`, {
      method: 'PUT',
      body: JSON.stringify(data)
    })
  }

  async deleteSupplier(id: string): Promise<any> {
    return this.request(`/suppliers/${id}`, {
      method: 'DELETE'
    })
  }

  // ==========================================
  // SETTINGS
  // ==========================================
  async getSettings(): Promise<any> {
    try {
      return this.request('/settings')
    } catch (error) {
      console.warn('Settings API not available:', error)
      return {
        success: true,
        data: {
          settings: {
            storeName: 'BitsTech POS',
            storeEmail: '<EMAIL>',
            storePhone: '+95-9-***********',
            storeAddress: 'Yangon, Myanmar',
            defaultTaxRate: 0,
            primaryCurrency: 'MMK',
            defaultTheme: 'light',
            defaultLanguage: 'en'
          },
          exchangeRates: {
            MMK: { rate: 1, symbol: 'K', name: 'Myanmar Kyat', flag: '🇲🇲' },
            USD: { rate: 0.00048, symbol: '$', name: 'US Dollar', flag: '🇺🇸' },
            THB: { rate: 0.016, symbol: '฿', name: 'Thai Baht', flag: '🇹🇭' }
          }
        }
      }
    }
  }

  async updateSettings(data: any): Promise<any> {
    return this.request('/settings', {
      method: 'PUT',
      body: JSON.stringify(data)
    })
  }

  async updateGeneralSettings(data: any): Promise<any> {
    const response = await this.request<any>('/settings/general', {
      method: 'PUT',
      body: JSON.stringify(data)
    })
    return response
  }

  async updateTaxSettings(data: any): Promise<any> {
    const response = await this.request<any>('/settings/tax', {
      method: 'PUT',
      body: JSON.stringify(data)
    })
    return response
  }

  async updateCurrencySettings(data: any): Promise<any> {
    const response = await this.request<any>('/settings/currency', {
      method: 'PUT',
      body: JSON.stringify(data)
    })
    return response
  }

  async updateThemeSettings(data: any): Promise<any> {
    const response = await this.request<any>('/settings/theme', {
      method: 'PUT',
      body: JSON.stringify(data)
    })
    return response
  }

  // Exchange Rates methods
  async getExchangeRates(): Promise<any> {
    try {
      return this.request('/exchange-rates')
    } catch (error) {
      console.warn('Exchange rates API not available:', error)
      return {
        success: true,
        data: {
          rates: {
            MMK: { rate: 1, symbol: 'K', name: 'Myanmar Kyat', flag: '🇲🇲' },
            USD: { rate: 0.00048, symbol: '$', name: 'US Dollar', flag: '🇺🇸' },
            THB: { rate: 0.016, symbol: '฿', name: 'Thai Baht', flag: '🇹🇭' }
          }
        }
      }
    }
  }

  async updateExchangeRates(rates: any): Promise<any> {
    const response = await this.request<any>('/exchange-rates', {
      method: 'PUT',
      body: JSON.stringify({ rates })
    })
    return response
  }

  // Live exchange rates removed - users control rates manually

  // User management methods
  async getUsers(params?: {
    page?: number
    limit?: number
    role?: string
    status?: string
    search?: string
  }): Promise<any> {
    try {
      return await this.get('/users', params)
    } catch (error) {
      console.error('Users API not available:', error)
      throw new Error('Backend server required for users data. Please start the backend server.')
    }
  }

  async createUser(userData: any): Promise<any> {
    try {
      return await this.request('/users', {
        method: 'POST',
        body: JSON.stringify(userData)
      })
    } catch (error) {
      console.error('Create user API not available:', error)
      throw new Error('Backend server required for creating users. Please start the backend server.')
    }
  }

  async updateUser(id: string, userData: any): Promise<any> {
    try {
      return await this.request(`/users/${id}`, {
        method: 'PUT',
        body: JSON.stringify(userData)
      })
    } catch (error) {
      console.error('Update user API not available:', error)
      throw new Error('Backend server required for updating users. Please start the backend server.')
    }
  }

  async deleteUser(id: string): Promise<any> {
    try {
      return await this.request(`/users/${id}`, {
        method: 'DELETE'
      })
    } catch (error) {
      console.error('Delete user API not available:', error)
      throw new Error('Backend server required for deleting users. Please start the backend server.')
    }
  }

  async updateUserStatus(id: string, isActive: boolean): Promise<any> {
    try {
      return await this.request(`/users/${id}/status`, {
        method: 'PUT',
        body: JSON.stringify({ isActive })
      })
    } catch (error) {
      console.error('Update user status API not available:', error)
      throw new Error('Backend server required for updating user status. Please start the backend server.')
    }
  }

  // Security settings
  async updatePassword(currentPassword: string, newPassword: string): Promise<any> {
    if (false) { // USE_MOCK_API disabled
      await new Promise(resolve => setTimeout(resolve, 1000))

      // Get current user data
      const currentUser = JSON.parse(localStorage.getItem('bitstech_current_user') || '{}')
      const storedPassword = localStorage.getItem('bitstech_user_password') || 'admin123'

      // Validate current password
      if (currentPassword !== storedPassword) {
        return {
          success: false,
          message: 'Current password is incorrect',
          error: 'INVALID_PASSWORD'
        }
      }

      // Validate new password strength
      if (newPassword.length < 8) {
        return {
          success: false,
          message: 'Password must be at least 8 characters long',
          error: 'WEAK_PASSWORD'
        }
      }

      // Store new password
      localStorage.setItem('bitstech_user_password', newPassword)

      // Log security event
      const securityLogs = JSON.parse(localStorage.getItem('bitstech_security_logs') || '[]')
      securityLogs.unshift({
        id: Date.now().toString(),
        event: 'PASSWORD_CHANGED',
        user: currentUser.email || 'Unknown',
        timestamp: new Date().toISOString(),
        ip: '127.0.0.1',
        status: 'success',
        details: 'Password successfully changed'
      })
      localStorage.setItem('bitstech_security_logs', JSON.stringify(securityLogs.slice(0, 100))) // Keep last 100 logs

      return {
        success: true,
        message: 'Password updated successfully'
      }
    }

    try {
      return await this.request('/auth/change-password', {
        method: 'POST',
        body: JSON.stringify({ currentPassword, newPassword })
      })
    } catch (error) {
      console.warn('Change password API not available, using local storage:', error)
      return this.updatePassword(currentPassword, newPassword) // Fallback to mock implementation
    }
  }

  async getSecurityLogs(params?: {
    page?: number
    limit?: number
    status?: string
    search?: string
  }): Promise<any> {
    if (false) { // USE_MOCK_API disabled
      await new Promise(resolve => setTimeout(resolve, 500))

      // Get stored security logs
      let logs = JSON.parse(localStorage.getItem('bitstech_security_logs') || '[]')

      // If no logs, create some default ones
      if (logs.length === 0) {
        logs = [
          {
            id: '1',
            event: 'USER_LOGIN',
            user: '<EMAIL>',
            timestamp: new Date().toISOString(),
            ip: '*************',
            status: 'success',
            details: 'Successful login'
          },
          {
            id: '2',
            event: 'FAILED_LOGIN',
            user: '<EMAIL>',
            timestamp: new Date(Date.now() - 30 * 60 * 1000).toISOString(),
            ip: '************',
            status: 'failed',
            details: 'Invalid credentials'
          },
          {
            id: '3',
            event: 'SETTINGS_CHANGED',
            user: '<EMAIL>',
            timestamp: new Date(Date.now() - 60 * 60 * 1000).toISOString(),
            ip: '*************',
            status: 'success',
            details: 'Security settings updated'
          }
        ]
        localStorage.setItem('bitstech_security_logs', JSON.stringify(logs))
      }

      // Apply filters
      let filteredLogs = logs

      if (params?.status && params?.status !== 'all') {
        filteredLogs = filteredLogs.filter((log: any) => log.status === params?.status)
      }

      if (params?.search) {
        const searchTerm = params!.search!.toLowerCase()
        filteredLogs = filteredLogs.filter((log: any) =>
          log.event.toLowerCase().includes(searchTerm) ||
          log.user.toLowerCase().includes(searchTerm) ||
          log.ip.includes(searchTerm) ||
          log.details.toLowerCase().includes(searchTerm)
        )
      }

      // Apply pagination
      const page = params?.page || 1
      const limit = params?.limit || 20
      const startIndex = (page - 1) * limit
      const endIndex = startIndex + limit
      const paginatedLogs = filteredLogs.slice(startIndex, endIndex)

      return {
        success: true,
        data: paginatedLogs,
        pagination: {
          page,
          limit,
          total: filteredLogs.length,
          pages: Math.ceil(filteredLogs.length / limit)
        }
      }
    }

    try {
      return await this.get('/security/logs', params)
    } catch (error) {
      console.warn('Security logs API not available, using local data:', error)
      return this.getSecurityLogs(params) // Fallback to mock data
    }
  }

  async updateSecuritySettings(settings: any): Promise<any> {
    if (false) { // USE_MOCK_API disabled
      await new Promise(resolve => setTimeout(resolve, 1000))

      // Store security settings
      localStorage.setItem('bitstech_security_settings', JSON.stringify(settings))

      // Log security event
      const currentUser = JSON.parse(localStorage.getItem('bitstech_current_user') || '{}')
      const securityLogs = JSON.parse(localStorage.getItem('bitstech_security_logs') || '[]')
      securityLogs.unshift({
        id: Date.now().toString(),
        event: 'SECURITY_SETTINGS_UPDATED',
        user: currentUser.email || 'Unknown',
        timestamp: new Date().toISOString(),
        ip: '127.0.0.1',
        status: 'success',
        details: 'Security settings configuration updated'
      })
      localStorage.setItem('bitstech_security_logs', JSON.stringify(securityLogs.slice(0, 100)))

      return {
        success: true,
        message: 'Security settings updated successfully',
        data: settings
      }
    }

    try {
      return await this.request('/security/settings', {
        method: 'PUT',
        body: JSON.stringify(settings)
      })
    } catch (error) {
      console.warn('Security settings API not available, using local storage:', error)
      return this.updateSecuritySettings(settings) // Fallback to mock implementation
    }
  }

  async getSecuritySettings(): Promise<any> {
    if (false) { // USE_MOCK_API disabled
      await new Promise(resolve => setTimeout(resolve, 300))

      // Get stored security settings
      const storedSettings = localStorage.getItem('bitstech_security_settings')
      let settings = {
        twoFactorAuth: false,
        sessionTimeout: 60,
        ipWhitelist: '',
        auditLogging: true,
        autoBackup: true,
        backupFrequency: 'daily',
        encryptData: true,
        passwordPolicy: {
          requireComplexPassword: true,
          passwordExpiry: 90,
          maxLoginAttempts: 3,
          lockoutDuration: 30
        }
      }

      if (storedSettings) {
        try {
          settings = { ...settings, ...JSON.parse(storedSettings!) }
        } catch (error) {
          console.warn('Error parsing stored security settings:', error)
        }
      }

      return {
        success: true,
        data: settings
      }
    }

    try {
      return await this.get('/security/settings')
    } catch (error) {
      console.warn('Security settings API not available, using local data:', error)
      return this.getSecuritySettings() // Fallback to mock data
    }
  }

  // Receipt Templates
  async getReceiptTemplates(): Promise<any> {
    try {
      const response = await this.request('/settings/receipts/templates')
      return response
    } catch (error) {
      console.error('Error fetching receipt templates:', error)
      // Fallback to default templates
      await new Promise(resolve => setTimeout(resolve, 300))

      // Get stored templates
      const storedTemplates = localStorage.getItem('bitstech_receipt_templates')
      let templates = []

      if (storedTemplates) {
        try {
          templates = JSON.parse(storedTemplates)
        } catch (error) {
          console.warn('Error parsing stored receipt templates:', error)
          templates = []
        }
      }

      // If no templates, create default ones
      if (templates.length === 0) {
        templates = [
          {
            id: 'default',
            name: 'Default Receipt',
            description: 'Standard receipt template with company logo',
            isDefault: true,
            headerLogo: true,
            headerText: 'BitsTech Computer Store\nThank you for your purchase!',
            footerText: 'Visit us again!\nwww.bitstech.com',
            showQR: true,
            showBarcode: false,
            paperSize: 'thermal-80',
            fontSize: 'medium',
            alignment: 'center',
            showTax: true,
            showDiscount: true,
            showCustomerInfo: false,
            createdAt: new Date().toISOString()
          },
          {
            id: 'minimal',
            name: 'Minimal Receipt',
            description: 'Clean and simple receipt design',
            isDefault: false,
            headerLogo: false,
            headerText: 'BitsTech Store',
            footerText: 'Thank you!',
            showQR: false,
            showBarcode: true,
            paperSize: 'thermal-58',
            fontSize: 'small',
            alignment: 'left',
            showTax: true,
            showDiscount: false,
            showCustomerInfo: false,
            createdAt: new Date().toISOString()
          },
          {
            id: 'professional',
            name: 'Professional Invoice',
            description: 'Professional business invoice design with modern layout',
            isDefault: false,
            headerLogo: true,
            headerText: 'BitsTech Computer Store\nProfessional IT Solutions',
            footerText: 'Thank You For Your Business\n\n+95 9 ***********\<EMAIL>\nYour location here\n\nTerms & Conditions:\nPayment due within 30 days. Late payments may incur additional charges.',
            showQR: false,
            showBarcode: false,
            paperSize: 'a4',
            fontSize: 'medium',
            alignment: 'left',
            showTax: true,
            showDiscount: true,
            showCustomerInfo: true,
            isProfessional: true,
            createdAt: new Date().toISOString()
          }
        ]
        localStorage.setItem('bitstech_receipt_templates', JSON.stringify(templates))
      }

      return {
        success: true,
        data: templates
      }
    }

    try {
      return await this.get('/receipt-templates')
    } catch (error) {
      console.warn('Receipt templates API not available, using local data:', error)
      return this.getReceiptTemplates() // Fallback to mock data
    }
  }

  async createReceiptTemplate(templateData: any): Promise<any> {
    try {
      const response = await this.request('/settings/receipts/templates', {
        method: 'POST',
        body: JSON.stringify(templateData)
      })
      return response
    } catch (error) {
      console.error('Error creating receipt template:', error)
      // Fallback to local storage
      await new Promise(resolve => setTimeout(resolve, 800))

      // Get existing templates
      const storedTemplates = localStorage.getItem('bitstech_receipt_templates')
      let templates = []

      if (storedTemplates) {
        try {
          templates = JSON.parse(storedTemplates)
        } catch (error) {
          console.warn('Error parsing stored receipt templates:', error)
          templates = []
        }
      }

      // Check if name already exists
      const nameExists = templates.some((template: any) => template.name === templateData.name)
      if (nameExists) {
        return {
          success: false,
          message: 'Template name already exists',
          error: 'DUPLICATE_NAME'
        }
      }

      // Create new template
      const newTemplate = {
        id: `template_${Date.now()}`,
        ...templateData,
        isDefault: false,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      }

      // Add to templates array
      templates.push(newTemplate)

      // Save to localStorage
      localStorage.setItem('bitstech_receipt_templates', JSON.stringify(templates))

      return {
        success: true,
        message: 'Receipt template created successfully',
        data: newTemplate
      }
    }

    // No API fallback needed - already handled above
    return { success: false, error: 'Template creation failed' }
  }

  async updateReceiptTemplate(id: string, templateData: any): Promise<any> {
    try {
      const response = await this.request(`/settings/receipts/templates/${id}`, {
        method: 'PUT',
        body: JSON.stringify(templateData)
      })
      return response
    } catch (error) {
      console.error('Error updating receipt template:', error)
      // Fallback to local storage
      await new Promise(resolve => setTimeout(resolve, 800))

      // Get existing templates
      const storedTemplates = localStorage.getItem('bitstech_receipt_templates')
      let templates = []

      if (storedTemplates) {
        try {
          templates = JSON.parse(storedTemplates)
        } catch (error) {
          console.warn('Error parsing stored receipt templates:', error)
          return {
            success: false,
            message: 'Error accessing template data'
          }
        }
      }

      // Find template to update
      const templateIndex = templates.findIndex((template: any) => template.id === id)
      if (templateIndex === -1) {
        return {
          success: false,
          message: 'Template not found',
          error: 'TEMPLATE_NOT_FOUND'
        }
      }

      // Check for duplicate name (excluding current template)
      if (templateData.name) {
        const nameExists = templates.some((template: any) => template.name === templateData.name && template.id !== id)
        if (nameExists) {
          return {
            success: false,
            message: 'Template name already exists',
            error: 'DUPLICATE_NAME'
          }
        }
      }

      // Update template
      const updatedTemplate = {
        ...templates[templateIndex],
        ...templateData,
        updatedAt: new Date().toISOString()
      }

      templates[templateIndex] = updatedTemplate

      // Save to localStorage
      localStorage.setItem('bitstech_receipt_templates', JSON.stringify(templates))

      return {
        success: true,
        message: 'Receipt template updated successfully',
        data: updatedTemplate
      }
    }

    // No API fallback needed - already handled above
    return { success: false, error: 'Template update failed' }
  }

  async deleteReceiptTemplate(id: string): Promise<any> {
    try {
      const response = await this.request(`/settings/receipts/templates/${id}`, {
        method: 'DELETE'
      })
      return response
    } catch (error) {
      console.error('Error deleting receipt template:', error)
      // Fallback to local storage
      await new Promise(resolve => setTimeout(resolve, 500))

      // Get existing templates
      const storedTemplates = localStorage.getItem('bitstech_receipt_templates')
      let templates = []

      if (storedTemplates) {
        try {
          templates = JSON.parse(storedTemplates)
        } catch (error) {
          console.warn('Error parsing stored receipt templates:', error)
          return {
            success: false,
            message: 'Error accessing template data'
          }
        }
      }

      // Find template to delete
      const templateIndex = templates.findIndex((template: any) => template.id === id)
      if (templateIndex === -1) {
        return {
          success: false,
          message: 'Template not found',
          error: 'TEMPLATE_NOT_FOUND'
        }
      }

      // Check if template is default (prevent deletion)
      if (templates[templateIndex].isDefault) {
        return {
          success: false,
          message: 'Cannot delete default template',
          error: 'DEFAULT_DELETE_FORBIDDEN'
        }
      }

      // Remove template from array
      templates.splice(templateIndex, 1)

      // Save to localStorage
      localStorage.setItem('bitstech_receipt_templates', JSON.stringify(templates))

      return {
        success: true,
        message: 'Receipt template deleted successfully'
      }
    }

    // No API fallback needed - already handled above
    return { success: false, error: 'Template deletion failed' }
  }

  async setDefaultReceiptTemplate(id: string): Promise<any> {
    try {
      const response = await this.request(`/settings/receipts/templates/${id}/set-default`, {
        method: 'PUT'
      })
      return response
    } catch (error) {
      console.error('Error setting default receipt template:', error)
      // Fallback to local storage
      await new Promise(resolve => setTimeout(resolve, 500))

      // Get existing templates
      const storedTemplates = localStorage.getItem('bitstech_receipt_templates')
      let templates = []

      if (storedTemplates) {
        try {
          templates = JSON.parse(storedTemplates)
        } catch (error) {
          console.warn('Error parsing stored receipt templates:', error)
          return {
            success: false,
            message: 'Error accessing template data'
          }
        }
      }

      // Find template to set as default
      const templateIndex = templates.findIndex((template: any) => template.id === id)
      if (templateIndex === -1) {
        return {
          success: false,
          message: 'Template not found',
          error: 'TEMPLATE_NOT_FOUND'
        }
      }

      // Update all templates - remove default from others, set for selected
      templates = templates.map((template: any) => ({
        ...template,
        isDefault: template.id === id,
        updatedAt: new Date().toISOString()
      }))

      // Save to localStorage
      localStorage.setItem('bitstech_receipt_templates', JSON.stringify(templates))

      return {
        success: true,
        message: 'Default template updated successfully',
        data: templates[templateIndex]
      }
    }

    // No API fallback needed - already handled above
    return { success: false, error: 'Set default template failed' }
  }

  // Notifications
  async getNotificationSettings(): Promise<any> {
    try {
      return await this.get('/notifications/settings')
    } catch (error) {
      console.warn('Notification settings API not available:', error)
      return {
        success: true,
        data: {
          emailNotifications: true,
          pushNotifications: true,
          smsNotifications: false,
          soundEnabled: true,
          quietHours: {
            enabled: false,
            start: '22:00',
            end: '08:00'
          }
        }
      }
    }
  }

  async updateNotificationSettings(settings: any): Promise<any> {
    try {
      return await this.request('/notifications/settings', {
        method: 'PUT',
        body: JSON.stringify(settings)
      })
    } catch (error) {
      console.warn('Update notification settings API not available:', error)
      return {
        success: false,
        error: 'Notification settings API not available'
      }
    }
  }

  async testNotification(type: string, channel: string): Promise<any> {
    try {
      return await this.request('/notifications/test', {
        method: 'POST',
        body: JSON.stringify({ type, channel })
      })
    } catch (error) {
      console.warn('Test notification API not available:', error)
      return {
        success: false,
        error: 'Test notification API not available'
      }
    }
  }

  // Database Backup
  async createBackup(options?: { includeImages?: boolean, compress?: boolean }): Promise<any> {
    try {
      return await this.request('/database/backup', {
        method: 'POST',
        body: JSON.stringify(options || {})
      })
    } catch (error) {
      console.warn('Database backup API not available:', error)
      return {
        success: false,
        error: 'Database backup API not available'
      }
    }
  }

  async getBackups(): Promise<any> {
    try {
      return await this.get('/database/backups')
    } catch (error) {
      console.warn('Get backups API not available:', error)
      return {
        success: true,
        data: []
      }
    }
  }

  async restoreBackup(backupId: string): Promise<any> {
    try {
      return await this.request(`/database/restore/${backupId}`, {
        method: 'POST'
      })
    } catch (error) {
      console.warn('Restore backup API not available:', error)
      return {
        success: false,
        error: 'Restore backup API not available'
      }
    }
  }

  async deleteBackup(backupId: string): Promise<any> {
    try {
      return await this.request(`/database/backups/${backupId}`, {
        method: 'DELETE'
      })
    } catch (error) {
      console.warn('Delete backup API not available:', error)
      return {
        success: false,
        error: 'Delete backup API not available'
      }
    }
  }

  // Forecasting methods - Real Database Only
  async getForecastingData(params?: any): Promise<any> {
    if (FORCE_REAL_DATABASE_ONLY) {
      // Force real database only - no fallback
      const queryParams = new URLSearchParams()

      if (params) {
        Object.entries(params).forEach(([key, value]) => {
          if (value !== undefined && value !== null) {
            queryParams.append(key, value.toString())
          }
        })
      }

      const endpoint = `/forecasting${queryParams.toString() ? `?${queryParams.toString()}` : ''}`
      return this.request(endpoint)
    }

    try {
      const queryParams = new URLSearchParams()

      if (params) {
        Object.entries(params).forEach(([key, value]) => {
          if (value !== undefined && value !== null) {
            queryParams.append(key, value.toString())
          }
        })
      }

      const endpoint = `/forecasting${queryParams.toString() ? `?${queryParams.toString()}` : ''}`
      return this.request(endpoint)
    } catch (error) {
      console.error('Forecasting API failed:', error)
      throw error // Don't fallback to mock data
    }
  }

  async resetForecastingData(): Promise<any> {
    try {
      return this.request('/forecasting/reset', {
        method: 'POST'
      })
    } catch (error) {
      console.warn('Reset forecasting API not available:', error)
      return {
        success: false,
        error: 'Reset forecasting API not available'
      }
    }
  }

  // Analytics methods
  async getAnalyticsData(params?: any): Promise<any> {
    try {
      const queryParams = new URLSearchParams()

      if (params) {
        Object.entries(params).forEach(([key, value]) => {
          if (value !== undefined && value !== null) {
            queryParams.append(key, value.toString())
          }
        })
      }

      const endpoint = `/analytics${queryParams.toString() ? `?${queryParams.toString()}` : ''}`
      return this.request(endpoint)
    } catch (error) {
      console.warn('Analytics API not available:', error)
      return {
        success: true,
        data: {
          salesAnalytics: [],
          customerAnalytics: [],
          productAnalytics: [],
          revenueAnalytics: []
        }
      }
    }
  }

  // KPIs methods
  async getKPIData(params?: any): Promise<any> {
    try {
      const queryParams = new URLSearchParams()

      if (params) {
        Object.entries(params).forEach(([key, value]) => {
          if (value !== undefined && value !== null) {
            queryParams.append(key, value.toString())
          }
        })
      }

      const endpoint = `/kpis${queryParams.toString() ? `?${queryParams.toString()}` : ''}`
      return this.request(endpoint)
    } catch (error) {
      console.warn('KPIs API not available:', error)
      return {
        success: true,
        data: {
          salesKPIs: [],
          customerKPIs: [],
          inventoryKPIs: [],
          financialKPIs: []
        }
      }
    }
  }

  // Sales metrics methods
  async getSalesMetrics(params?: any): Promise<any> {
    try {
      const queryParams = new URLSearchParams()

      if (params) {
        Object.entries(params).forEach(([key, value]) => {
          if (value !== undefined && value !== null) {
            queryParams.append(key, value.toString())
          }
        })
      }

      const endpoint = `/sales/metrics${queryParams.toString() ? `?${queryParams.toString()}` : ''}`
      return this.request(endpoint)
    } catch (error) {
      console.warn('Sales metrics API not available:', error)
      return {
        success: true,
        data: {
          totalRevenue: 0,
          totalSales: 0,
          totalCustomers: 0,
          totalProducts: 0,
          avgOrderValue: 0,
          profitMargin: 0,
          growthRate: 0,
          topSellingProduct: 'N/A'
        }
      }
    }
  }

  // Health check
  async healthCheck(): Promise<any> {
    try {
      const response = await fetch(`${this.baseURL.replace('/api', '')}/health`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
        signal: AbortSignal.timeout(5000) // 5 second timeout
      })

      if (!response.ok) {
        return { success: false, error: `HTTP ${response.status}`, status: 'error' }
      }

      return await response.json()
    } catch (error) {
      // Silent error handling for health check
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Health check failed',
        status: 'error',
        message: 'Backend server not available',
        offline: true
      }
    }
  }
}

// Create and export the API client instance
const apiClient = new ApiClient()

export default apiClient
export { ApiClient, apiClient }
export type { ApiResponse, LoginRequest, RegisterRequest, User, AuthResponse }
