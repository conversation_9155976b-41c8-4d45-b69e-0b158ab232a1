'use client'

import { useState, useEffect, useCallback, useMemo } from 'react'
import { useRouter } from 'next/navigation'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip'
import { useAuth } from '@/contexts/auth-context'
import { useTheme } from '@/contexts/theme-context'
import { useCurrency } from '@/contexts/currency-context'
// Core POS Components
import apiClient from '@/lib/api'
import {
  Search,
  Plus,
  Minus,
  Trash2,
  ShoppingCart,
  CreditCard,
  Scan,
  Grid3X3,
  List,
  Eye,
  EyeOff,
  Sun,
  Moon,
  DollarSign,
  Globe,
  Clock,
  Wifi,
  WifiOff,
  Settings,
  LayoutDashboard,
  LogOut,
  Filter,
  Star,
  Heart,
  Zap,
  TrendingUp,
  Package,
  Smartphone,
  Building,
  Banknote,
  QrCode,
  Wallet,
  X,
  ChevronRight,
  ChevronLeft,
  Home,
  FileText,
  BarChart3,
  Menu,
  Bell,
  Calculator,
  Receipt,
  ChevronsRight,
  ChevronsLeft,
  Maximize2,
  Minimize2,
  RefreshCw,
  AlertCircle,
  CheckCircle,
  Info,
  ArrowLeft,
  ArrowRight,
  Edit,
  Save,
  UserPlus,
  MapPin,
  Phone,
  Mail,
  Calendar,
  Tag,
  Percent,
  Truck,
  CreditCard as CardIcon,
  Coins,
  Printer,
  Download,
  Share2,
  Copy,
  Check,
  AlertTriangle,
  HelpCircle,
  Keyboard,
  Monitor,
  Mouse,
  HardDrive,
  Headphones,
  Laptop,
  User,
  Users,
  Barcode
} from 'lucide-react'

interface Product {
  _id: string
  name: string
  price: number
  currency: string
  category: {
    _id: string
    name: string
    color: string
    icon: string
  }
  sku: string
  inventory: {
    quantity: number
    unit: string
  }
  image?: string
  isActive: boolean
}

interface CartItem {
  product: Product
  quantity: number
  totalPrice: number
}

interface PaymentMethod {
  id: string
  name: string
  icon: any
  color: string
  qrCode?: string
}

interface Customer {
  _id?: string
  name: string
  email?: string
  phone: string
  address?: string
  loyaltyPoints?: number
  customerType?: 'regular' | 'vip' | 'wholesale' | 'new'
}

interface SaleData {
  saleNumber: string
  customer: Customer
  items: CartItem[]
  subtotal: number
  tax: number
  discount: number
  shipping: number
  total: number
  paymentMethod: string
  timestamp: Date
  currency?: string
  language?: string
}

export default function POSPage() {
  const { user, isAuthenticated, isLoading } = useAuth()
  const { theme, language, toggleTheme, toggleLanguage } = useTheme()
  const { currentCurrency, formatCurrency, convertPrice, exchangeRates, setCurrency } = useCurrency()
  const router = useRouter()

  // Mock user for demo
  const mockUser = {
    email: '<EMAIL>',
    name: 'Cashier User',
    role: 'cashier'
  }

  const currentUser = user || mockUser

  // Core States
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedCategory, setSelectedCategory] = useState('all')
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid')
  const [cart, setCart] = useState<CartItem[]>([])
  const [isProcessing, setIsProcessing] = useState(false)
  const [products, setProducts] = useState<Product[]>([])
  const [categories, setCategories] = useState<any[]>([])
  const [customers, setCustomers] = useState<Customer[]>([])
  const [loading, setLoading] = useState(true)
  const [isOnline, setIsOnline] = useState(true)
  const [currentTime, setCurrentTime] = useState<Date | null>(null)
  const [backendStatus, setBackendStatus] = useState<'checking' | 'online' | 'offline'>('checking')

  // UI States
  const [isCartVisible, setIsCartVisible] = useState(false)

  // Customer & Payment States
  const [selectedCustomer, setSelectedCustomer] = useState<Customer | null>(null)
  const [selectedProductDetails, setSelectedProductDetails] = useState<any>(null)
  const [isProductDetailsOpen, setIsProductDetailsOpen] = useState(false)
  const [recentSales, setRecentSales] = useState<SaleData[]>([])
  const [selectedPaymentMethod, setSelectedPaymentMethod] = useState<string | null>(null)
  const [showPaymentModal, setShowPaymentModal] = useState(false)
  const [verificationPassword, setVerificationPassword] = useState('')
  const [paymentData, setPaymentData] = useState({
    // KBZ Pay, Wave Money, NUG Pay
    phoneNumber: '',
    accountName: '',
    qrCode: '',
    // Card
    cardNumber: '',
    cardholderName: '',
    transactionId: '',
    // Bank
    bankName: '',
    accountNumber: '',
    accountHolderName: ''
  })

  // Load payment data from API/settings instead of localStorage
  useEffect(() => {
    const loadPaymentSettings = async () => {
      try {
        const response = await apiClient.getSettings()
        if (response.success && response.data.paymentMethods) {
          setPaymentData(response.data.paymentMethods)
        }
      } catch (error) {
        console.error('Error loading payment settings:', error)
      }
    }
    loadPaymentSettings()
  }, [])

  // Save payment data to API/settings instead of localStorage
  useEffect(() => {
    const savePaymentSettings = async () => {
      if (paymentData.phoneNumber || paymentData.accountName || paymentData.qrCode ||
          paymentData.bankName || paymentData.accountNumber || paymentData.accountHolderName ||
          paymentData.cardNumber || paymentData.cardholderName || paymentData.transactionId) {
        try {
          await apiClient.updateSettings({
            paymentMethods: paymentData
          })
        } catch (error) {
          console.error('Error saving payment settings:', error)
        }
      }
    }
    savePaymentSettings()
  }, [paymentData])

  // Customer form states
  const [customerName, setCustomerName] = useState('')
  const [customerPhone, setCustomerPhone] = useState('')
  const [customerAddress, setCustomerAddress] = useState('')
  const [showCustomerModal, setShowCustomerModal] = useState(false)
  const [customerSearchTerm, setCustomerSearchTerm] = useState('')

  // Settings States
  const [taxRate, setTaxRate] = useState(5)
  const [shippingCost, setShippingCost] = useState(0)
  const [discountRate, setDiscountRate] = useState(0)

  // Payment methods
  const paymentMethods: PaymentMethod[] = [
    { id: 'kbz', name: 'KBZ Pay', icon: Smartphone, color: 'bg-blue-500', qrCode: 'kbz-qr-code' },
    { id: 'wave', name: 'Wave Money', icon: Zap, color: 'bg-purple-500', qrCode: 'wave-qr-code' },
    { id: 'nug', name: 'NUG Pay', icon: Star, color: 'bg-yellow-500', qrCode: 'nug-qr-code' },
    { id: 'bank', name: 'Thailand Bank', icon: Building, color: 'bg-red-500' },
    { id: 'card', name: 'Card', icon: CreditCard, color: 'bg-indigo-500' },
    { id: 'cash', name: 'Cash', icon: Wallet, color: 'bg-gray-500' }
  ]

  // Bank options for Thailand
  const bankOptions = [
    'Bangkok Bank',
    'Kasikorn Bank',
    'Siam Commercial Bank',
    'Krung Thai Bank',
    'Other'
  ]

  // Currency will be handled by currency context



  // Text translations
  const t = {
    title: language === 'mm' ? 'BitsTech POS စနစ်' : 'BitsTech POS System',
    subtitle: language === 'mm' ? 'ကွန်ပျူတာနှင့် အပိုပစ္စည်းများ' : 'Computer & Accessories Store',
    search: language === 'mm' ? 'ကုန်ပစ္စည်းရှာရန်...' : 'Search products...',
    allCategories: language === 'mm' ? 'အမျိုးအစားအားလုံး' : 'All Categories',
    addToCart: language === 'mm' ? 'ခြင်းတောင်းထဲထည့်ရန်' : 'Add to Cart',
    cart: language === 'mm' ? 'ခြင်းတောင်း' : 'Shopping Cart',
    orderDetails: language === 'mm' ? 'အော်ဒါ အသေးစိတ်များ' : 'Order Details',
    total: language === 'mm' ? 'စုစုပေါင်း' : 'Total',
    items: language === 'mm' ? 'ပစ္စည်းများ' : 'items',
    checkout: language === 'mm' ? 'ငွေချေရန်' : 'Checkout',
    processing: language === 'mm' ? 'လုပ်ဆောင်နေသည်...' : 'Processing...',
    clearCart: language === 'mm' ? 'ခြင်းတောင်းရှင်းရန်' : 'Clear Cart',
    noItems: language === 'mm' ? 'ခြင်းတောင်းထဲ ပစ္စည်းမရှိပါ' : 'No items in cart',
    inStock: language === 'mm' ? 'ရှိသည်' : 'In Stock',
    outOfStock: language === 'mm' ? 'ကုန်သည်' : 'Out of Stock',
    customer: language === 'mm' ? 'ဖောက်သည်' : 'Customer',
    customerName: language === 'mm' ? 'ဖောက်သည်အမည်' : 'Customer Name',
    customerPhone: language === 'mm' ? 'ဖုန်းနံပါတ်' : 'Phone Number',
    tax: language === 'mm' ? 'အခွန်' : 'Tax',
    discount: language === 'mm' ? 'လျှော့စျေး' : 'Discount',
    shipping: language === 'mm' ? 'ပို့ဆောင်ခ' : 'Shipping',
    paymentMethod: language === 'mm' ? 'ငွေချေနည်း' : 'Payment Method',
    selectPayment: language === 'mm' ? 'ငွေချေနည်းရွေးရန်' : 'Select Payment Method',
    dashboard: language === 'mm' ? 'ဒက်ရှ်ဘုတ်' : 'Dashboard',
    products: language === 'mm' ? 'ကုန်ပစ္စည်းများ' : 'Products',
    sales: language === 'mm' ? 'ရောင်းချမှုများ' : 'Sales',
    reports: language === 'mm' ? 'အစီရင်ခံစာများ' : 'Reports',
    customers: language === 'mm' ? 'ဖောက်သည်များ' : 'Customers',
    settings: language === 'mm' ? 'ဆက်တင်များ' : 'Settings',
    notifications: language === 'mm' ? 'အကြောင်းကြားချက်များ' : 'Notifications',
    profile: language === 'mm' ? 'ပရိုဖိုင်' : 'Profile',
    logout: language === 'mm' ? 'ထွက်ရန်' : 'Logout',
    home: language === 'mm' ? 'ပင်မစာမျက်နှာ' : 'Home',
    back: language === 'mm' ? 'နောက်သို့' : 'Back',
    next: language === 'mm' ? 'ရှေ့သို့' : 'Next',
    save: language === 'mm' ? 'သိမ်းရန်' : 'Save',
    cancel: language === 'mm' ? 'ပယ်ဖျက်ရန်' : 'Cancel',
    edit: language === 'mm' ? 'ပြင်ဆင်ရန်' : 'Edit',
    delete: language === 'mm' ? 'ဖျက်ရန်' : 'Delete',
    view: language === 'mm' ? 'ကြည့်ရန်' : 'View',
    add: language === 'mm' ? 'ထည့်ရန်' : 'Add',
    update: language === 'mm' ? 'အပ်ဒိတ်လုပ်ရန်' : 'Update',
    refresh: language === 'mm' ? 'ပြန်လည်ဆန်းသစ်ရန်' : 'Refresh',
    loading: language === 'mm' ? 'ဖွင့်နေသည်...' : 'Loading...',
    success: language === 'mm' ? 'အောင်မြင်ပါသည်' : 'Success',
    error: language === 'mm' ? 'အမှားအယွင်း' : 'Error',
    warning: language === 'mm' ? 'သတိပေးချက်' : 'Warning',
    info: language === 'mm' ? 'အချက်အလက်' : 'Information'
  }

  // Utility Functions - formatCurrency is now from currency context

  // Check if payment data is complete
  const isPaymentDataComplete = useCallback(() => {
    if (!selectedPaymentMethod) return false

    switch (selectedPaymentMethod) {
      case 'kbz':
        return paymentData.phoneNumber && paymentData.accountName && paymentData.qrCode
      case 'wave':
        return paymentData.phoneNumber && paymentData.accountName && paymentData.qrCode
      case 'nug':
        return paymentData.phoneNumber && paymentData.accountName && paymentData.qrCode
      case 'card':
        return paymentData.cardNumber && paymentData.cardholderName && paymentData.transactionId
      case 'bank':
        return paymentData.bankName && paymentData.accountNumber && paymentData.accountHolderName
      case 'cash':
        return true // Cash doesn't require additional data
      default:
        return false
    }
  }, [selectedPaymentMethod, paymentData])

  const formatTime = useCallback((date: Date | null) => {
    if (!date) return 'Loading...'
    return date.toLocaleTimeString(language === 'mm' ? 'my-MM' : 'en-US', {
      hour: '2-digit',
      minute: '2-digit',
      hour12: true
    })
  }, [language])

  const playSound = useCallback((type: 'success' | 'error' | 'notification') => {
    console.log(`Playing ${type} sound`)
  }, [])

  // Product Details Handler
  const handleProductDetails = (product: any) => {
    setSelectedProductDetails(product)
    setIsProductDetailsOpen(true)
    playSound('notification')
  }

  // Cart Functions (will be overridden by API functions below)

  // Calculation Functions
  const getTotalItems = useCallback(() => {
    return cart.reduce((total, item) => total + item.quantity, 0)
  }, [cart])

  const getSubtotal = useCallback(() => {
    return cart.reduce((total, item) => total + item.totalPrice, 0)
  }, [cart])

  const getTaxAmount = useCallback(() => {
    return getSubtotal() * (taxRate / 100)
  }, [getSubtotal, taxRate])

  const getDiscountAmount = useCallback(() => {
    return getSubtotal() * (discountRate / 100)
  }, [getSubtotal, discountRate])

  const getShippingAmount = useCallback(() => {
    return shippingCost
  }, [shippingCost])

  const getTotalAmount = useCallback(() => {
    return getSubtotal() + getTaxAmount() - getDiscountAmount() + getShippingAmount()
  }, [getSubtotal, getTaxAmount, getDiscountAmount, getShippingAmount])



  // Payment Processing
  const processPayment = useCallback(async () => {
    if (!selectedCustomer || cart.length === 0 || !selectedPaymentMethod) {
      playSound('error')
      return
    }

    setIsProcessing(true)

    try {
      // Prepare checkout data
      const checkoutData = {
        customer: {
          name: selectedCustomer.name,
          phone: selectedCustomer.phone,
          email: selectedCustomer.email || '',
          address: typeof selectedCustomer.address === 'string'
            ? selectedCustomer.address
            : (selectedCustomer.address && typeof selectedCustomer.address === 'object' && 'street' in selectedCustomer.address
                ? (selectedCustomer.address as any).street
                : ''
              ) || ''
        },
        paymentMethod: selectedPaymentMethod,
        paymentDetails: paymentData,
        taxRate,
        discountRate,
        shippingCost,
        notes: ''
      }

      // Validate checkout first
      const validationResponse = await apiClient.validateCheckout(checkoutData)
      if (!validationResponse.success) {
        console.error('Checkout validation failed:', validationResponse.error)
        playSound('error')
        alert(validationResponse.error || 'Checkout validation failed')
        return
      }

      // Process checkout
      const checkoutResponse = await apiClient.checkout(checkoutData)
      if (!checkoutResponse.success) {
        console.error('Checkout failed:', checkoutResponse.error)
        playSound('error')
        alert(checkoutResponse.error || 'Checkout failed')
        return
      }

      // Success - prepare comprehensive sale data for invoice
      const saleData: SaleData = {
        saleNumber: checkoutResponse.data.saleNumber,
        customer: selectedCustomer,
        items: cart,
        subtotal: getSubtotal(),
        tax: getTaxAmount(),
        discount: getDiscountAmount(),
        shipping: getShippingAmount(),
        total: getTotalAmount(),
        paymentMethod: paymentMethods.find(m => m.id === selectedPaymentMethod)?.name || 'Unknown',
        timestamp: new Date(),
        currency: currentCurrency,
        language: language
      }

      // Enhanced checkout data with all POS information for invoice
      const enhancedCheckoutData = {
        saleNumber: checkoutResponse.data.saleNumber,
        saleId: checkoutResponse.data.saleId,
        timestamp: new Date().toISOString(),
        customerDetails: {
          name: customerName || selectedCustomer.name,
          phone: customerPhone || selectedCustomer.phone,
          address: customerAddress || (typeof selectedCustomer.address === 'string'
            ? selectedCustomer.address
            : (selectedCustomer.address && typeof selectedCustomer.address === 'object' && 'street' in selectedCustomer.address
                ? (selectedCustomer.address as any).street
                : ''
              )) || 'Yangon, Myanmar',
          email: selectedCustomer.email || ''
        },
        cartItems: cart.map(item => ({
          productId: item.product._id,
          productName: item.product.name,
          productSku: item.product.sku || 'N/A',
          unitPrice: item.product.price,
          quantity: item.quantity,
          totalPrice: item.totalPrice,
          category: item.product.category?.name || 'General'
        })),
        subtotal: getSubtotal(),
        discount: getDiscountAmount(),
        tax: getTaxAmount(),
        shipping: getShippingAmount(),
        total: getTotalAmount(),
        paymentMethod: paymentMethods.find(m => m.id === selectedPaymentMethod)?.name || 'Cash',
        paymentMethodId: selectedPaymentMethod,
        paymentDetails: paymentData,
        currency: currentCurrency,
        language: language,
        posSettings: {
          taxRate,
          discountRate,
          shippingCost
        }
      }

      // Save sale data to database instead of localStorage
      try {
        const saleResponse = await apiClient.createSale(enhancedCheckoutData)
        if (saleResponse.success) {
          console.log('Sale saved to database:', saleResponse.data)
          setRecentSales(prev => [saleData, ...prev.slice(0, 4)])

          // Store sale ID for invoice page
          const saleId = saleResponse.data._id || saleResponse.data.id
          if (saleId) {
            sessionStorage.setItem('lastSaleId', saleId)
          }
        }
      } catch (error) {
        console.error('Error saving sale to database:', error)
      }

      // Update dashboard data with real sale information
      // updateDashboardWithSale(saleData) // Commented out as function not available

      console.log('Checkout completed, sale data saved to database')
      console.log('Customer details:', enhancedCheckoutData.customerDetails)
      console.log('Cart items:', enhancedCheckoutData.cartItems)
      console.log('Payment info:', enhancedCheckoutData.paymentMethod)

      // Reset payment modal states but keep payment data for next use
      setShowPaymentModal(false)
      setVerificationPassword('')
      setSelectedPaymentMethod(null)

      // Clear cart after successful checkout (don't restore inventory - it's sold)
      try {
        const clearResponse = await apiClient.clearCart()
        if (clearResponse.success) {
          // Clear cart state without restoring inventory (products are sold)
          setCart([])
          setSelectedCustomer(null)
          setCustomerName('')
          setCustomerPhone('')
          setCustomerAddress('')
        }
      } catch (clearError) {
        console.error('Error clearing cart:', clearError)
      }

      playSound('success')

      // Redirect to invoice page with sale ID
      const saleId = checkoutResponse.data.saleId
      if (saleId) {
        window.location.href = `/invoice?saleId=${saleId}`
      } else {
        // Fallback to invoice page without sale ID (will use sample data)
        window.location.href = '/invoice'
      }

    } catch (error) {
      console.error('Payment processing error:', error)
      playSound('error')
      alert('Payment processing failed. Please try again.')
    } finally {
      setIsProcessing(false)
    }
  }, [selectedCustomer, cart, selectedPaymentMethod, paymentMethods, paymentData, taxRate, discountRate, shippingCost, getSubtotal, getTaxAmount, getDiscountAmount, getShippingAmount, getTotalAmount, playSound])

  // Load data from API with authentication check
  useEffect(() => {
    const loadData = async () => {
      try {
        setLoading(true)
        console.log('🔄 Loading data from API...')

        // Check if user is authenticated, if not, auto-login with demo credentials
        if (!apiClient.isAuthenticated()) {
          console.log('🔐 No authentication found, attempting auto-login...')
          try {
            await apiClient.login({
              email: '<EMAIL>',
              password: 'admin123'
            })
            console.log('✅ Auto-login successful')
          } catch (loginError) {
            console.warn('⚠️ Auto-login failed, continuing with fallback mode:', loginError)
          }
        }

        // Load real data from API with timeout
        const [categoriesResponse, productsResponse, customersResponse] = await Promise.all([
          apiClient.getCategories(),
          apiClient.getProducts(),
          apiClient.getCustomers()
        ])

        console.log('📂 Categories response:', categoriesResponse)
        console.log('📦 Products response:', productsResponse)
        console.log('👥 Customers response:', customersResponse)

        // Handle categories response
        if (categoriesResponse.success) {
          const categoriesData = Array.isArray(categoriesResponse.data) ? categoriesResponse.data : []
          setCategories(categoriesData)

          if (categoriesResponse.message?.includes('Backend not available')) {
            console.log('⚠️ Categories: Backend not available, using empty data')
          } else {
            console.log('✅ Categories loaded:', categoriesData.length, 'categories')
          }
        } else {
          console.error('❌ Failed to load categories:', categoriesResponse.error)
          setCategories([])
        }

        // Handle customers response
        if (customersResponse.success) {
          const customersData = Array.isArray(customersResponse.data) ? customersResponse.data : []
          setCustomers(customersData)
          console.log('✅ Customers loaded:', customersData.length, 'customers')
        } else {
          console.error('❌ Failed to load customers:', customersResponse.error)
          setCustomers([])
        }

        // Handle products response
        if (productsResponse.success) {
          const productsData = Array.isArray(productsResponse.data) ? productsResponse.data : []
          setProducts(productsData)

          if (productsResponse.message?.includes('Backend not available')) {
            console.log('⚠️ Products: Backend not available, using empty data')
          } else {
            console.log('✅ Products loaded:', productsData.length, 'products')
          }
        } else {
          console.error('❌ Failed to load products:', productsResponse.error)
          setProducts([])
        }

        // Update backend status based on responses
        if (categoriesResponse.message?.includes('Backend not available') ||
            productsResponse.message?.includes('Backend not available')) {
          console.log('ℹ️ POS System running in offline mode - Backend server not available')
          setBackendStatus('offline')
          setIsOnline(false)
        } else {
          console.log('✅ Backend server is online and responding')
          setBackendStatus('online')
          setIsOnline(true)
        }

      } catch (error) {
        console.error('❌ Error loading data:', error)
        // Fallback to empty arrays if API fails
        setCategories([])
        setProducts([])
      } finally {
        console.log('✅ Data loading completed')
        setLoading(false)
      }
    }

    loadData()
  }, [])

  // Listen for storage changes to detect when products are reset
  useEffect(() => {
    const handleStorageChange = (event: StorageEvent) => {
      // If products were removed or reset flag was set, reload data
      if (event.key === 'bitstech_products' || event.key === 'bitstech_system_reset') {
        console.log('Products reset detected, reloading data...')
        // Reload products from API
        const reloadProducts = async () => {
          try {
            const productsResponse = await apiClient.getProducts()
            if (productsResponse.success) {
              const productsData = Array.isArray(productsResponse.data) ? productsResponse.data : []
              setProducts(productsData)
              console.log('Products reloaded after reset:', productsData.length)
            }
          } catch (error) {
            console.error('Error reloading products after reset:', error)
            setProducts([])
          }
        }
        reloadProducts()
      }
    }

    window.addEventListener('storage', handleStorageChange)
    return () => window.removeEventListener('storage', handleStorageChange)
  }, [])

  // Load cart from API
  useEffect(() => {
    const loadCart = async () => {
      try {
        const cartResponse = await apiClient.getCart()
        if (cartResponse.success && cartResponse.data && cartResponse.data.items) {
          setCart(cartResponse.data.items)
        }
      } catch (error) {
        console.warn('Cart API not available, using local cart:', error)
        // Use local cart state instead of API
      }
    }

    loadCart()
  }, [])

  // Real-time currency sync
  useEffect(() => {
    const handleCurrencyChange = (event: CustomEvent) => {
      const newCurrency = event.detail.currency
      console.log('🔄 POS currency changed to:', newCurrency)
      // POS will automatically re-render with new currency formatting
      // Cart totals will be recalculated automatically
    }

    window.addEventListener('global-currency-sync', handleCurrencyChange as EventListener)
    return () => {
      window.removeEventListener('global-currency-sync', handleCurrencyChange as EventListener)
    }
  }, [])



  // Cart API Functions
  const addToCart = useCallback(async (product: Product) => {
    if (product.inventory.quantity === 0) {
      playSound('error')
      return
    }

    try {
      console.log('Adding to cart:', product.name)
      const response = await apiClient.addToCart(product._id, 1)
      if (response.success) {
        // Update cart state
        setCart(prevCart => {
          const existingItem = prevCart.find(item => item.product._id === product._id)

          if (existingItem) {
            // Update quantity
            return prevCart.map(item =>
              item.product._id === product._id
                ? { ...item, quantity: item.quantity + 1, totalPrice: (item.quantity + 1) * item.product.price }
                : item
            )
          } else {
            // Add new item
            const newItem = {
              product: product,
              quantity: 1,
              totalPrice: product.price
            }
            return [...prevCart, newItem]
          }
        })

        // Update product inventory quantity in products state
        setProducts(prevProducts =>
          prevProducts.map(p =>
            p._id === product._id
              ? { ...p, inventory: { ...p.inventory, quantity: p.inventory.quantity - 1 } }
              : p
          )
        )

        playSound('success')
        console.log('Successfully added to cart')
      } else {
        console.error('Failed to add to cart:', response.error)
        playSound('error')
      }
    } catch (error) {
      console.error('Error adding to cart:', error)
      playSound('error')
    }
  }, [playSound])

  const updateQuantity = useCallback(async (productId: string, newQuantity: number) => {
    if (newQuantity <= 0) {
      // Call removeFromCart directly
      try {
        const response = await apiClient.removeFromCart(productId)
        if (response.success) {
          // Get current cart item to restore inventory
          const cartItem = cart.find(item => item.product._id === productId)
          if (cartItem) {
            // Restore inventory quantity
            setProducts(prevProducts =>
              prevProducts.map(p =>
                p._id === productId
                  ? { ...p, inventory: { ...p.inventory, quantity: p.inventory.quantity + cartItem.quantity } }
                  : p
              )
            )
          }

          // Remove from cart
          setCart(prevCart => prevCart.filter(item => item.product._id !== productId))
          playSound('notification')
        } else {
          console.error('Failed to remove from cart:', response.error)
        }
      } catch (error) {
        console.error('Error removing from cart:', error)
      }
      return
    }

    try {
      const response = await apiClient.updateCartItem(productId, newQuantity)
      if (response.success) {
        // Get current cart item to calculate inventory change
        const currentCartItem = cart.find(item => item.product._id === productId)
        if (currentCartItem) {
          const quantityDifference = newQuantity - currentCartItem.quantity

          // Update inventory based on quantity change
          setProducts(prevProducts =>
            prevProducts.map(p =>
              p._id === productId
                ? { ...p, inventory: { ...p.inventory, quantity: p.inventory.quantity - quantityDifference } }
                : p
            )
          )
        }

        // Update cart
        setCart(prevCart =>
          prevCart.map(item =>
            item.product._id === productId
              ? { ...item, quantity: newQuantity, totalPrice: newQuantity * item.product.price }
              : item
          )
        )
      } else {
        console.error('Failed to update cart:', response.error)
      }
    } catch (error) {
      console.error('Error updating cart:', error)
    }
  }, [playSound, cart])

  const removeFromCart = useCallback(async (productId: string) => {
    try {
      const response = await apiClient.removeFromCart(productId)
      if (response.success) {
        // Get current cart item to restore inventory
        const cartItem = cart.find(item => item.product._id === productId)
        if (cartItem) {
          // Restore inventory quantity
          setProducts(prevProducts =>
            prevProducts.map(p =>
              p._id === productId
                ? { ...p, inventory: { ...p.inventory, quantity: p.inventory.quantity + cartItem.quantity } }
                : p
            )
          )
        }

        // Remove from cart
        setCart(prevCart => prevCart.filter(item => item.product._id !== productId))
        playSound('notification')
      } else {
        console.error('Failed to remove from cart:', response.error)
      }
    } catch (error) {
      console.error('Error removing from cart:', error)
    }
  }, [playSound, cart])

  const clearCart = useCallback(async () => {
    try {
      const response = await apiClient.clearCart()
      if (response.success) {
        // Restore inventory for all cart items
        cart.forEach(cartItem => {
          setProducts(prevProducts =>
            prevProducts.map(p =>
              p._id === cartItem.product._id
                ? { ...p, inventory: { ...p.inventory, quantity: p.inventory.quantity + cartItem.quantity } }
                : p
            )
          )
        })

        // Clear cart and customer
        setCart([])
        setSelectedCustomer(null)
        playSound('notification')
      } else {
        console.error('Failed to clear cart:', response.error)
      }
    } catch (error) {
      console.error('Error clearing cart:', error)
    }
  }, [playSound, cart])

  // Time update - client-side only to prevent hydration mismatch
  useEffect(() => {
    // Set initial time on client
    setCurrentTime(new Date())

    const timer = setInterval(() => {
      setCurrentTime(new Date())
    }, 1000)
    return () => clearInterval(timer)
  }, [])

  // Load customer data when selected customer changes
  useEffect(() => {
    if (selectedCustomer) {
      setCustomerName(selectedCustomer.name || '')
      setCustomerPhone(selectedCustomer.phone || '')
      setCustomerAddress(typeof selectedCustomer.address === 'string'
        ? selectedCustomer.address
        : (selectedCustomer.address && typeof selectedCustomer.address === 'object' && 'street' in selectedCustomer.address
            ? (selectedCustomer.address as any).street
            : ''
          ) || '')
    }
  }, [selectedCustomer])

  // Customer Management Functions
  const createNewCustomer = useCallback(async () => {
    if (!customerName || !customerPhone) {
      alert(language === 'mm' ? 'ဖောက်သည်အမည်နှင့် ဖုန်းနံပါတ် လိုအပ်ပါသည်' : 'Customer name and phone are required')
      return
    }

    try {
      const customerData = {
        name: customerName,
        phone: customerPhone,
        address: customerAddress || '',
        customerType: 'regular'
      }

      const response = await apiClient.createCustomer(customerData)
      if (response.success) {
        const newCustomer = response.data
        setCustomers(prev => [...prev, newCustomer])
        setSelectedCustomer(newCustomer)
        setShowCustomerModal(false)

        // Clear form
        setCustomerName('')
        setCustomerPhone('')
        setCustomerAddress('')

        console.log('✅ New customer created:', newCustomer.name)
        playSound('success')
      } else {
        console.error('❌ Failed to create customer:', response.error)
        alert(response.error || 'Failed to create customer')
      }
    } catch (error) {
      console.error('❌ Error creating customer:', error)
      alert('Error creating customer')
    }
  }, [customerName, customerPhone, customerAddress, language, playSound])

  // Filtered Products - Memoized for performance
  const filteredProducts = useMemo(() => {
    return products.filter(product => {
      const matchesSearch = product.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           product.sku.toLowerCase().includes(searchTerm.toLowerCase())
      const matchesCategory = selectedCategory === 'all' || product.category._id === selectedCategory
      return matchesSearch && matchesCategory && product.isActive
    })
  }, [products, searchTerm, selectedCategory])

  // Filtered Customers - Memoized for performance
  const filteredCustomers = useMemo(() => {
    return customers.filter(customer =>
      customer.name.toLowerCase().includes(customerSearchTerm.toLowerCase()) ||
      customer.phone.includes(customerSearchTerm)
    )
  }, [customers, customerSearchTerm])





  return (
    <TooltipProvider>
      <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 dark:from-gray-900 dark:via-slate-900 dark:to-gray-800 flex flex-col">
        {/* Mobile/Desktop Layout */}
        <div className="flex flex-col lg:flex-row flex-1">
          {/* Main Content Area */}
          <div className={`flex-1 flex flex-col transition-all duration-300 ${isCartVisible ? 'lg:mr-80' : ''}`}>
            {/* Header Section */}
            <header className="bg-white/95 dark:bg-gray-900/95 backdrop-blur-sm border-b border-gray-200 dark:border-gray-700 shadow-sm">
              {/* Title Section */}
              <div className="px-4 py-4 border-b border-gray-100 dark:border-gray-800">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-600 rounded-xl flex items-center justify-center">
                      <Package className="h-6 w-6 text-white" />
                    </div>
                    <div>
                      <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
                        {t.title}
                      </h1>
                      <p className="text-sm text-gray-500 dark:text-gray-400">
                        {t.subtitle}
                      </p>
                    </div>
                  </div>

                  {/* Mobile Cart Button */}
                  <div className="lg:hidden">
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <Button
                          variant={isCartVisible ? "default" : "outline"}
                          size="sm"
                          onClick={() => setIsCartVisible(!isCartVisible)}
                          className="relative transition-all duration-300 hover:scale-110 hover:shadow-lg group"
                        >
                          <ShoppingCart className="h-5 w-5 group-hover:animate-bounce" />
                          {cart.length > 0 && (
                            <Badge className="absolute -top-2 -right-2 h-5 w-5 rounded-full p-0 text-xs bg-red-500 text-white animate-pulse">
                              {getTotalItems()}
                            </Badge>
                          )}
                        </Button>
                      </TooltipTrigger>
                      <TooltipContent>
                        <p>{isCartVisible ? 'Hide Cart' : 'Show Cart'} ({getTotalItems()} items)</p>
                      </TooltipContent>
                    </Tooltip>
                  </div>
                </div>
              </div>

              {/* Search and Controls Section */}
              <div className="px-4 py-3">
                <div className="flex flex-col sm:flex-row items-center space-y-3 sm:space-y-0 sm:space-x-4">
                  {/* Search Bar */}
                  <div className="flex-1 w-full sm:max-w-md">
                    <div className="relative">
                      <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
                      <Input
                        type="text"
                        placeholder={t.search}
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                        className="pl-10 pr-4 py-2 w-full rounded-lg border-gray-300 dark:border-gray-600 focus:ring-2 focus:ring-blue-500"
                      />
                    </div>
                  </div>

                  {/* Control Icons */}
                  <div className="flex items-center space-x-2">
                    {/* Backend Status */}
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <div className={`
                          hidden sm:flex items-center space-x-2 px-3 py-2 rounded-lg transition-all duration-300
                          ${backendStatus === 'online'
                            ? 'bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800'
                            : backendStatus === 'offline'
                            ? 'bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800'
                            : 'bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800'
                          }
                          hover:scale-105 hover:shadow-md
                        `}>
                          {backendStatus === 'online' ? (
                            <Wifi className="h-4 w-4 text-green-600 dark:text-green-400 animate-pulse" />
                          ) : backendStatus === 'offline' ? (
                            <WifiOff className="h-4 w-4 text-yellow-600 dark:text-yellow-400" />
                          ) : (
                            <RefreshCw className="h-4 w-4 text-blue-600 dark:text-blue-400 animate-spin" />
                          )}
                          <span className={`text-sm font-medium ${
                            backendStatus === 'online'
                              ? 'text-green-700 dark:text-green-300'
                              : backendStatus === 'offline'
                              ? 'text-yellow-700 dark:text-yellow-300'
                              : 'text-blue-700 dark:text-blue-300'
                          }`}>
                            {backendStatus === 'online'
                              ? (language === 'mm' ? 'အွန်လိုင်း' : 'Online')
                              : backendStatus === 'offline'
                              ? (language === 'mm' ? 'အော့ဖ်လိုင်း' : 'Offline')
                              : (language === 'mm' ? 'စစ်ဆေးနေ' : 'Checking')
                            }
                          </span>
                        </div>
                      </TooltipTrigger>
                      <TooltipContent>
                        <p>
                          {backendStatus === 'online'
                            ? (language === 'mm' ? 'ဆာဗာ ချိတ်ဆက်ပြီး' : 'Backend server connected')
                            : backendStatus === 'offline'
                            ? (language === 'mm' ? 'ဆာဗာ မရရှိ - Offline မုဒ်' : 'Backend server not available - Offline mode')
                            : (language === 'mm' ? 'ဆာဗာ ချိတ်ဆက်မှု စစ်ဆေးနေ' : 'Checking backend server connection')
                          }
                        </p>
                      </TooltipContent>
                    </Tooltip>

                    {/* Enhanced Time Display */}
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <div className="hidden sm:flex flex-col items-center space-y-1 px-4 py-2 bg-gradient-to-br from-blue-50 via-purple-50 to-pink-50 dark:from-blue-900/20 dark:via-purple-900/20 dark:to-pink-900/20 border border-blue-200 dark:border-blue-800 rounded-xl transition-all duration-300 hover:scale-105 hover:shadow-lg backdrop-blur-sm">
                          <div className="flex items-center space-x-2">
                            <Clock className="h-4 w-4 text-blue-600 dark:text-blue-400 animate-pulse" />
                            <span className="text-sm font-bold text-blue-700 dark:text-blue-300">
                              {currentTime ? (
                                <>
                                  {currentTime.toLocaleDateString('en-US', {
                                    weekday: 'long',
                                    year: 'numeric',
                                    month: '2-digit',
                                    day: '2-digit'
                                  })}, {currentTime.toLocaleTimeString('en-US', {
                                    hour12: true,
                                    hour: '2-digit',
                                    minute: '2-digit'
                                  })} +0630
                                </>
                              ) : (
                                'Loading...'
                              )}
                            </span>
                          </div>

                        </div>
                      </TooltipTrigger>
                      <TooltipContent>
                        <p>Current time</p>
                      </TooltipContent>
                    </Tooltip>

                    {/* Control Buttons */}
                    <div className="flex items-center space-x-1 bg-gray-100 dark:bg-gray-800 rounded-lg p-1 border border-gray-200 dark:border-gray-700">
                      {/* Theme Toggle */}
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={toggleTheme}
                            className="h-8 w-8 p-0 hover:bg-gray-200 dark:hover:bg-gray-700 transition-all duration-300 hover:scale-110 hover:rotate-12"
                          >
                            {theme === 'dark' ? (
                              <Sun className="h-4 w-4 text-yellow-500 animate-spin" />
                            ) : (
                              <Moon className="h-4 w-4 text-blue-600 animate-pulse" />
                            )}
                          </Button>
                        </TooltipTrigger>
                        <TooltipContent>
                          <p>{theme === 'dark' ? 'Switch to Light Mode' : 'Switch to Dark Mode'}</p>
                        </TooltipContent>
                      </Tooltip>

                      {/* Language Toggle */}
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={toggleLanguage}
                            className="h-8 w-8 p-0 hover:bg-gray-200 dark:hover:bg-gray-700 transition-all duration-300 hover:scale-110 hover:rotate-12"
                          >
                            <Globe className="h-4 w-4 text-green-600 dark:text-green-400 animate-bounce" />
                          </Button>
                        </TooltipTrigger>
                        <TooltipContent>
                          <p>Language: {language === 'mm' ? 'မြန်မာ' : language === 'en' ? 'English' : 'ไทย'}</p>
                        </TooltipContent>
                      </Tooltip>

                      {/* Currency Toggle */}
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => {
                              const currencies = ['MMK', 'USD', 'THB']
                              const currentIndex = currencies.indexOf(currentCurrency)
                              const nextIndex = (currentIndex + 1) % currencies.length
                              setCurrency(currencies[nextIndex] as any)
                              console.log('Currency changed to:', currencies[nextIndex])
                            }}
                            className="h-8 w-8 p-0 hover:bg-gray-200 dark:hover:bg-gray-700 transition-all duration-300 hover:scale-110 hover:rotate-12"
                          >
                            <span className="text-sm animate-pulse">
                              {currentCurrency === 'USD' ? '🇺🇸' : currentCurrency === 'THB' ? '🇹🇭' : '🇲🇲'}
                            </span>
                          </Button>
                        </TooltipTrigger>
                        <TooltipContent>
                          <p>Currency: {currentCurrency === 'USD' ? 'US Dollar' : currentCurrency === 'THB' ? 'Thai Baht' : 'Myanmar Kyat'}</p>
                        </TooltipContent>
                      </Tooltip>

                      {/* Login/User Icon */}
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => {
                              // Navigate to dashboard based on role
                              window.location.href = '/dashboard'
                            }}
                            className="h-8 w-8 p-0 hover:bg-gray-200 dark:hover:bg-gray-700 transition-all duration-300 hover:scale-110"
                          >
                            <User className="h-4 w-4 text-purple-600 dark:text-purple-400 animate-pulse" />
                          </Button>
                        </TooltipTrigger>
                        <TooltipContent>
                          <p>Admin Dashboard</p>
                        </TooltipContent>
                      </Tooltip>
                    </div>

                    {/* Desktop Cart Button */}
                    <div className="hidden lg:block">
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <Button
                            variant={isCartVisible ? "default" : "outline"}
                            size="sm"
                            onClick={() => setIsCartVisible(!isCartVisible)}
                            className="relative px-4 py-2 transition-all duration-300 hover:scale-105 hover:shadow-lg group"
                          >
                            <ShoppingCart className="h-4 w-4 mr-2 group-hover:animate-bounce" />
                            <span className="font-medium">{t.cart}</span>
                            {cart.length > 0 && (
                              <Badge className="absolute -top-2 -right-2 h-5 w-5 rounded-full p-0 text-xs bg-red-500 text-white animate-pulse">
                                {getTotalItems()}
                              </Badge>
                            )}
                          </Button>
                        </TooltipTrigger>
                        <TooltipContent>
                          <p>{isCartVisible ? 'Hide Cart' : 'Show Cart'} ({getTotalItems()} items)</p>
                        </TooltipContent>
                      </Tooltip>
                    </div>
                  </div>
                </div>
              </div>
            </header>

            {/* Categories and Products Section */}
            <main className="flex-1 overflow-hidden flex flex-col">
              {/* Category Filter Bar */}
              <div className="bg-white/80 dark:bg-gray-900/80 backdrop-blur-sm border-b border-gray-200 dark:border-gray-700 p-4">
                <div className="flex items-center space-x-3 overflow-x-auto pb-2">
                  <Button
                    variant={selectedCategory === 'all' ? 'default' : 'outline'}
                    size="sm"
                    onClick={() => setSelectedCategory('all')}
                    className="whitespace-nowrap shadow-sm"
                  >
                    <Filter className="h-4 w-4 mr-2" />
                    {t.allCategories}
                  </Button>
                  {categories.map((category) => (
                    <Button
                      key={category._id}
                      variant={selectedCategory === category._id ? 'default' : 'outline'}
                      size="sm"
                      onClick={() => setSelectedCategory(category._id)}
                      className="whitespace-nowrap shadow-sm"
                      style={{
                        backgroundColor: selectedCategory === category._id ? category.color : undefined,
                        borderColor: category.color,
                        color: selectedCategory === category._id ? 'white' : undefined
                      }}
                    >
                      {category.name}
                    </Button>
                  ))}
                </div>
              </div>

              {/* Products Grid Container */}
              <div className="flex-1 p-4 sm:p-6 flex flex-col min-h-0">
                <div className="mb-4 flex-shrink-0">
                  <div className="flex items-center justify-between">
                    <h2 className="text-lg font-semibold text-gray-900 dark:text-white">
                      {language === 'mm' ? 'ကုန်ပစ္စည်းများ' : 'Products'}
                    </h2>
                    <div className="text-sm text-gray-500 dark:text-gray-400">
                      {filteredProducts.length} {language === 'mm' ? 'ပစ္စည်း' : 'items'}
                    </div>
                  </div>
                </div>

                {/* Products Grid Wrapper */}
                <div className="flex-1 overflow-y-auto min-h-0">
                  <div className="pb-4">

                {/* Products Grid - Enhanced */}
                <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 2xl:grid-cols-6 gap-6 auto-rows-max p-6">
                  {filteredProducts.map((product) => (
                      <Card
                      key={product._id}
                      className="group hover:shadow-xl hover:scale-105 transition-all duration-300 cursor-pointer bg-gradient-to-b from-gray-800 to-gray-900 dark:from-gray-900 dark:to-black border border-gray-700 dark:border-gray-600 rounded-2xl overflow-hidden select-none h-[380px] relative shadow-lg"
                      style={{ touchAction: 'manipulation' }}
                    >
                      <CardContent className="p-0 h-full flex flex-col">
                        {/* Product Image */}
                        <div className="w-full h-56 bg-gradient-to-br from-gray-700 via-gray-800 to-gray-900 flex items-center justify-center relative overflow-hidden">
                          {(() => {
                            const productImages = product.image ? [{ url: product.image, isPrimary: true }] : []
                            const primaryImage = productImages.find((img: any) => img.isPrimary) || productImages[0]
                            const hasImage = primaryImage && primaryImage.url

                            return hasImage ? (
                              <div className="relative w-full h-full">
                                <img
                                  src={primaryImage.url}
                                  alt={product.name}
                                  className="w-full h-full object-contain p-4 group-hover:scale-110 transition-transform duration-500"
                                  onError={(e) => {
                                    const target = e.target as HTMLImageElement
                                    target.style.display = 'none'
                                    const placeholder = target.parentElement?.parentElement?.querySelector('.pos-image-placeholder')
                                    if (placeholder) {
                                      placeholder.classList.remove('hidden')
                                    }
                                  }}
                                />
                              </div>
                            ) : null
                          })()}
                          <div className={`pos-image-placeholder absolute inset-0 flex flex-col items-center justify-center ${product.image ? 'hidden' : ''}`}>
                            <div className="w-16 h-16 bg-gray-600 rounded-full flex items-center justify-center mb-3">
                              <Package className="h-8 w-8 text-gray-300" />
                            </div>
                            <p className="text-sm text-gray-400 font-medium">No Image</p>
                          </div>


                        </div>

                        {/* Product Info */}
                        <div className="p-4 space-y-3 flex-1 flex flex-col">
                          {/* Product Name */}
                          <h3 className="font-bold text-lg text-white line-clamp-2 text-center leading-tight">
                            {product.name}
                          </h3>

                          {/* Price Section */}
                          <div className="text-center">
                            <span className="text-2xl font-bold text-orange-400">
                              {formatCurrency(product.price)}
                            </span>
                          </div>

                          {/* Stock Info */}
                          <div className="flex items-center justify-center space-x-2">
                            <div className={`flex items-center space-x-1 ${
                              product.inventory.quantity > 0 ? 'text-green-400' : 'text-red-400'
                            }`}>
                              <div className={`w-2 h-2 rounded-full ${
                                product.inventory.quantity > 0 ? 'bg-green-400' : 'bg-red-400'
                              } animate-pulse`}></div>
                              <span className="text-sm font-medium">
                                {product.inventory.quantity > 0 ? 'In Stock' : 'Sold'}
                              </span>
                            </div>
                            <span className="text-gray-400">•</span>
                            <span className="text-sm text-gray-400">
                              Stock: {product.inventory.quantity}
                            </span>
                          </div>

                          <div className="text-center">
                            <span className="text-sm text-gray-400">
                              Sold: 0
                            </span>
                          </div>

                          {/* Action Buttons */}
                          <div className="flex space-x-3 mt-auto">
                            <Button
                              variant="outline"
                              size="sm"
                              className="w-12 h-12 p-0 rounded-full border-2 border-gray-600 text-gray-300 hover:bg-gray-600 hover:text-white hover:border-gray-500 transition-all duration-300 group"
                              onClick={(e) => {
                                e.stopPropagation()
                                handleProductDetails(product)
                              }}
                            >
                              <Eye className="h-5 w-5 group-hover:scale-110 transition-transform duration-300" />
                            </Button>

                            <Button
                              size="sm"
                              className="flex-1 h-12 rounded-full bg-red-600 hover:bg-red-700 text-white border-0 shadow-lg hover:shadow-xl transition-all duration-300 group"
                              disabled={product.inventory.quantity === 0}
                              onClick={async (e) => {
                                e.preventDefault()
                                e.stopPropagation()
                                console.log('Add to cart clicked for:', product.name)
                                await addToCart(product)
                              }}
                            >
                              <ShoppingCart className="h-5 w-5 group-hover:animate-bounce" />
                            </Button>
                          </div>
                        </div>
                      </CardContent>
                      </Card>
                  ))}
                    </div>

                    {/* Empty State */}
                    {filteredProducts.length === 0 && (
                      <div className="text-center py-12">
                        <div className="max-w-md mx-auto">
                          {backendStatus === 'offline' ? (
                            <div className="space-y-4">
                              <div className="w-16 h-16 bg-yellow-100 dark:bg-yellow-900/20 rounded-full flex items-center justify-center mx-auto">
                                <WifiOff className="h-8 w-8 text-yellow-600 dark:text-yellow-400" />
                              </div>
                              <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
                                {language === 'mm' ? 'ဆာဗာ မရရှိပါ' : 'Backend Server Not Available'}
                              </h3>
                              <div className="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-4">
                                <p className="text-yellow-700 dark:text-yellow-300 text-sm mb-3">
                                  {language === 'mm'
                                    ? 'POS စနစ်သည် Offline မုဒ်တွင် အလုပ်လုပ်နေသည်။ ကုန်ပစ္စည်းများ ရယူရန် Backend server လိုအပ်ပါသည်။'
                                    : 'POS system is running in offline mode. Backend server is required to load products.'
                                  }
                                </p>
                                <div className="space-y-2 text-xs text-yellow-600 dark:text-yellow-400">
                                  <p>• {language === 'mm' ? 'Backend server ကို စတင်ပါ' : 'Start the backend server'}</p>
                                  <p>• {language === 'mm' ? 'Network connection စစ်ဆေးပါ' : 'Check network connection'}</p>
                                  <p>• {language === 'mm' ? 'API URL ကို စစ်ဆေးပါ' : 'Verify API URL configuration'}</p>
                                </div>
                              </div>
                            </div>
                          ) : (
                            <div className="space-y-4">
                              <Package className="h-16 w-16 text-gray-300 dark:text-gray-600 mx-auto" />
                              <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
                                {language === 'mm' ? 'ကုန်ပစ္စည်းမတွေ့ပါ' : 'No products found'}
                              </h3>
                              <p className="text-gray-500 dark:text-gray-400">
                                {language === 'mm' ? 'ရှာဖွေမှုကို ပြောင်းလဲကြည့်ပါ' : 'Try adjusting your search or filters'}
                              </p>
                            </div>
                          )}
                        </div>
                      </div>
                    )}
                  </div>
                </div>

                {/* Footer */}
                <footer className="mt-auto bg-white/80 dark:bg-gray-900/80 backdrop-blur-sm border-t border-gray-200 dark:border-gray-700 p-4 w-full">
                  <div className="flex flex-col sm:flex-row items-center justify-between space-y-2 sm:space-y-0">
                    <div className="flex items-center space-x-4 text-sm text-gray-600 dark:text-gray-400">
                      {/* Backend Status */}
                      <div className="flex items-center space-x-2">
                        <div className={`w-2 h-2 rounded-full ${
                          backendStatus === 'online'
                            ? 'bg-green-500 animate-pulse'
                            : backendStatus === 'offline'
                            ? 'bg-yellow-500 animate-bounce'
                            : 'bg-blue-500 animate-spin'
                        }`}></div>
                        <span>
                          {backendStatus === 'online'
                            ? (language === 'mm' ? 'ဆာဗာ အွန်လိုင်း' : 'Backend Online')
                            : backendStatus === 'offline'
                            ? (language === 'mm' ? 'အော့ဖ်လိုင်း မုဒ်' : 'Offline Mode')
                            : (language === 'mm' ? 'ချိတ်ဆက်နေ' : 'Connecting')
                          }
                        </span>
                      </div>

                      {/* Time */}
                      <div className="flex items-center space-x-2">
                        <Clock className="h-4 w-4" />
                        <span>{currentTime ? formatTime(currentTime) : 'Loading...'}</span>
                      </div>

                      {/* Products Count */}
                      <div className="flex items-center space-x-2">
                        <Package className="h-4 w-4" />
                        <span>
                          {language === 'mm' ? 'ကုန်ပစ္စည်း' : 'Products'}: {products.length}
                        </span>
                      </div>
                    </div>

                    <div className="flex items-center space-x-3 text-sm text-gray-500 dark:text-gray-400">
                      <div className="flex items-center space-x-2">
                        <Heart className="h-4 w-4 text-red-500 animate-pulse" />
                        <span>
                          Made with love by BitsTech
                        </span>
                      </div>
                      <div>
                        © 2024 BitsTech POS v1.0.0
                      </div>
                    </div>
                  </div>
                </footer>
              </div>
            </main>
          </div>

          {/* Cart Sidebar - Desktop Only - Compact */}
          {isCartVisible && (
            <aside className="hidden lg:flex w-80 border-l border-gray-200 dark:border-gray-700 bg-white/95 dark:bg-gray-900/95 backdrop-blur-sm transition-all duration-300 ease-in-out flex-col shadow-2xl fixed right-0 top-0 h-screen z-40 overflow-hidden">
              {/* Full Height Scrollable Container */}
              <div className="flex-1 flex flex-col h-full overflow-y-auto scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-transparent hover:scrollbar-thumb-gray-400 dark:scrollbar-thumb-gray-600 dark:hover:scrollbar-thumb-gray-500">
                {/* Mobile Header */}
                <div className="lg:hidden flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700 flex-shrink-0">
                  <h2 className="text-lg font-semibold text-gray-900 dark:text-white">
                    {t.cart}
                  </h2>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setIsCartVisible(false)}
                    className="h-8 w-8 p-0"
                  >
                    <X className="h-5 w-5" />
                  </Button>
                </div>

                {/* Desktop Header */}
                <div className="hidden lg:block bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-900/20 dark:to-purple-900/20 p-2 border-b border-gray-200 dark:border-gray-700 flex-shrink-0">
                <div className="flex items-center justify-between mb-1">
                  <div className="flex items-center space-x-2">
                    <ShoppingCart className="h-4 w-4 text-blue-600" />
                    <h2 className="text-sm font-semibold text-gray-900 dark:text-white">
                      {t.cart}
                    </h2>
                  </div>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setIsCartVisible(false)}
                    className="h-6 w-6 p-0"
                  >
                    <ChevronRight className="h-3 w-3" />
                  </Button>
                </div>
                <div className="flex items-center justify-between">
                  <p className="text-xs text-gray-600 dark:text-gray-400">
                    {getTotalItems()} {t.items} {language === 'mm' ? 'ရွေးချယ်ထားသည်' : 'selected'}
                  </p>
                  {cart.length > 0 && (
                    <div className="text-right">
                      <p className="text-xs text-gray-500 dark:text-gray-400">
                        {language === 'mm' ? 'စုစုပေါင်း' : 'Total'}
                      </p>
                      <p className="text-sm font-bold text-blue-600 dark:text-blue-400">
                        {formatCurrency(getTotalAmount())}
                      </p>
                    </div>
                  )}
                </div>
              </div>

                {/* Customer Details Section - Always Visible */}
                <div className="px-3 py-2">
                  <div className="bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm rounded-lg p-3 border border-gray-200 dark:border-gray-700 mb-3">
                    <div className="flex items-center space-x-2 mb-3">
                      <div className="w-6 h-6 bg-blue-100 dark:bg-blue-900/30 rounded-lg flex items-center justify-center">
                        <UserPlus className="h-4 w-4 text-blue-600 dark:text-blue-400" />
                      </div>
                      <h3 className="font-semibold text-sm text-gray-900 dark:text-white">
                        {language === 'mm' ? 'ဖောက်သည်အချက်အလက်' : 'Customer Details'}
                      </h3>
                    </div>

                    {/* Customer Selection */}
                    <div className="space-y-3">
                      {/* Customer Search/Select */}
                      <div>
                        <label className="flex items-center space-x-1 text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">
                          <Users className="h-3 w-3 text-blue-500" />
                          <span>{language === 'mm' ? 'ဖောက်သည်ရွေးချယ်ရန်' : 'Select Customer'}</span>
                        </label>
                        <div className="relative">
                          <Input
                            placeholder={language === 'mm' ? 'ဖောက်သည်ရှာရန်...' : 'Search customers...'}
                            value={customerSearchTerm}
                            onChange={(e) => setCustomerSearchTerm(e.target.value)}
                            className="h-8 text-xs border-gray-300 dark:border-gray-600 rounded-md pr-8"
                          />
                          <Search className="absolute right-2 top-1/2 transform -translate-y-1/2 h-3 w-3 text-gray-400" />
                        </div>

                        {/* Customer Dropdown */}
                        {customerSearchTerm && filteredCustomers.length > 0 && (
                          <div className="absolute z-50 w-full mt-1 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-md shadow-lg max-h-32 overflow-y-auto">
                            {filteredCustomers.slice(0, 5).map((customer) => (
                              <button
                                key={customer._id}
                                onClick={() => {
                                  setSelectedCustomer(customer)
                                  setCustomerName(customer.name)
                                  setCustomerPhone(customer.phone)
                                  setCustomerAddress(typeof customer.address === 'string'
                                    ? customer.address
                                    : (customer.address && typeof customer.address === 'object' && 'street' in customer.address
                                        ? (customer.address as any).street
                                        : ''
                                      ) || '')
                                  setCustomerSearchTerm('')
                                  playSound('success')
                                }}
                                className="w-full text-left px-3 py-2 hover:bg-gray-50 dark:hover:bg-gray-700 border-b border-gray-100 dark:border-gray-600 last:border-b-0"
                              >
                                <div className="text-xs font-medium text-gray-900 dark:text-white">{customer.name}</div>
                                <div className="text-xs text-gray-500 dark:text-gray-400">{customer.phone}</div>
                              </button>
                            ))}
                          </div>
                        )}
                      </div>

                      {/* Name Field */}
                      <div>
                        <label className="flex items-center space-x-1 text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">
                          <User className="h-3 w-3 text-blue-500" />
                          <span>{language === 'mm' ? 'အမည်' : 'Customer Name'}</span>
                          <span className="text-red-500">*</span>
                        </label>
                        <Input
                          placeholder={language === 'mm' ? 'ဖောက်သည်အမည်' : 'Enter name'}
                          value={customerName}
                          onChange={(e) => setCustomerName(e.target.value)}
                          className="h-8 text-xs border-gray-300 dark:border-gray-600 rounded-md"
                        />
                      </div>

                      {/* Phone Field */}
                      <div>
                        <label className="flex items-center space-x-1 text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">
                          <Phone className="h-3 w-3 text-green-500" />
                          <span>{language === 'mm' ? 'ဖုန်းနံပါတ်' : 'Phone No'}</span>
                          <span className="text-red-500">*</span>
                        </label>
                        <Input
                          placeholder={language === 'mm' ? 'ဖုန်းနံပါတ်' : 'Enter phone'}
                          value={customerPhone}
                          onChange={(e) => setCustomerPhone(e.target.value)}
                          className="h-8 text-xs border-gray-300 dark:border-gray-600 rounded-md"
                        />
                      </div>

                      {/* Address Field */}
                      <div>
                        <label className="flex items-center space-x-1 text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">
                          <MapPin className="h-3 w-3 text-orange-500" />
                          <span>{language === 'mm' ? 'လိပ်စာ' : 'Address'}</span>
                        </label>
                        <Input
                          placeholder={language === 'mm' ? 'လিပ်စာ' : 'Enter address'}
                          value={customerAddress}
                          onChange={(e) => setCustomerAddress(e.target.value)}
                          className="h-8 text-xs border-gray-300 dark:border-gray-600 rounded-md"
                        />
                      </div>

                      {/* Action Buttons */}
                      <div className="flex space-x-2 pt-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => {
                            setCustomerName('')
                            setCustomerPhone('')
                            setCustomerAddress('')
                            setSelectedCustomer(null)
                            setCustomerSearchTerm('')
                          }}
                          className="flex-1 h-8 text-xs border-gray-300 dark:border-gray-600"
                        >
                          <RefreshCw className="h-3 w-3 mr-1" />
                          {language === 'mm' ? 'ရှင်းလင်း' : 'Clear'}
                        </Button>

                        {selectedCustomer ? (
                          <Button
                            size="sm"
                            onClick={() => {
                              if (customerName.trim() && customerPhone.trim()) {
                                setSelectedCustomer({
                                  ...selectedCustomer,
                                  name: customerName.trim(),
                                  phone: customerPhone.trim(),
                                  address: customerAddress.trim()
                                })
                                playSound('success')
                              }
                            }}
                            className="flex-1 h-8 text-xs bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700"
                            disabled={!customerName.trim() || !customerPhone.trim()}
                          >
                            <Save className="h-3 w-3 mr-1" />
                            {language === 'mm' ? 'သိမ်း' : 'Update'}
                          </Button>
                        ) : (
                          <Button
                            size="sm"
                            onClick={createNewCustomer}
                            className="flex-1 h-8 text-xs bg-gradient-to-r from-green-500 to-blue-600 hover:from-green-600 hover:to-blue-700"
                            disabled={!customerName.trim() || !customerPhone.trim()}
                          >
                            <UserPlus className="h-3 w-3 mr-1" />
                            {language === 'mm' ? 'အသစ်ဖန်တီး' : 'Create New'}
                          </Button>
                        )}
                      </div>
                    </div>

                    {/* Customer Information Display */}
                    {selectedCustomer && (
                      <div className="mt-3 p-2 bg-green-50 dark:bg-green-900/20 rounded-md border border-green-200 dark:border-green-800">
                        <div className="flex items-center space-x-2 mb-2">
                          <Check className="h-4 w-4 text-green-600" />
                          <span className="text-xs font-medium text-green-800 dark:text-green-400">
                            {language === 'mm' ? 'ဖောက်သည်အချက်အလက်' : 'Customer Information'}
                          </span>
                        </div>
                        <div className="space-y-1">
                          <p className="text-xs text-gray-700 dark:text-gray-300">
                            <span className="font-medium">{language === 'mm' ? 'အမည်:' : 'Name:'}</span> {selectedCustomer.name}
                          </p>
                          <p className="text-xs text-gray-700 dark:text-gray-300">
                            <span className="font-medium">{language === 'mm' ? 'ဖုန်း:' : 'Phone:'}</span> {selectedCustomer.phone}
                          </p>
                          {selectedCustomer.address && (
                            <p className="text-xs text-gray-700 dark:text-gray-300">
                              <span className="font-medium">{language === 'mm' ? 'လိပ်စာ:' : 'Address:'}</span> {typeof selectedCustomer.address === 'string'
                                ? selectedCustomer.address
                                : (selectedCustomer.address && typeof selectedCustomer.address === 'object' && 'street' in selectedCustomer.address
                                    ? (selectedCustomer.address as any).street
                                    : ''
                                  )}
                            </p>
                          )}
                        </div>
                      </div>
                    )}

                    {!selectedCustomer && (
                      <div className="mt-3 p-2 bg-gray-50 dark:bg-gray-800/50 rounded-md border border-gray-200 dark:border-gray-700">
                        <p className="text-xs text-gray-500 dark:text-gray-400 text-center">
                          {language === 'mm' ? 'ဖောက်သည်အချက်အလက် မရှိပါ' : 'No customer information available.'}
                        </p>
                      </div>
                    )}
                  </div>
                </div>

                {/* Cart Items Section */}
                <div className="flex-1 px-3 py-2">
                  {cart.length === 0 ? (
                    <div className="flex items-center justify-center h-32">
                      <div className="text-center">
                        <div className="w-16 h-16 bg-gradient-to-br from-gray-100 to-gray-200 dark:from-gray-700 dark:to-gray-800 rounded-full flex items-center justify-center mx-auto mb-3 animate-pulse">
                          <ShoppingCart className="h-8 w-8 text-gray-400" />
                        </div>
                        <h3 className="text-sm font-medium text-gray-900 dark:text-white mb-1">
                          {language === 'mm' ? 'ခြင်းတောင်းအလွတ်' : 'Empty Cart'}
                        </h3>
                        <p className="text-gray-500 dark:text-gray-400 text-xs">
                          {language === 'mm' ? 'ကုန်ပစ္စည်းများကို ရွေးချယ်ပါ' : 'Add products to get started'}
                        </p>
                      </div>
                    </div>
                  ) : (
                    <div className="space-y-1">
                      {/* Cart Header */}
                      <div className="text-xs text-gray-500 dark:text-gray-400 px-0 pb-1">
                        {getTotalItems()} {language === 'mm' ? 'ပစ္စည်း ရွေးချယ်ထားသည်' : 'items selected'}
                      </div>

                      {/* Cart Items Table */}
                      <div className="space-y-1">
                      {cart.map((item, index) => (
                        <div
                          key={item.product._id}
                          className="bg-white/90 dark:bg-gray-800/90 backdrop-blur-sm border border-gray-200 dark:border-gray-700 rounded-lg p-2 hover:shadow-lg transition-all duration-300 group flex-shrink-0"
                        >
                          {/* Compact Product Row */}
                          <div className="flex items-center space-x-2">
                            {/* Index */}
                            <div className="w-5 h-5 bg-blue-100 dark:bg-blue-900/30 rounded-full flex items-center justify-center flex-shrink-0">
                              <span className="text-xs font-bold text-blue-600 dark:text-blue-400">
                                {index + 1}
                              </span>
                            </div>

                            {/* Product Image */}
                            <div className="w-10 h-12 bg-gradient-to-br from-gray-100 to-gray-200 dark:from-gray-700 dark:to-gray-800 rounded-md flex items-center justify-center flex-shrink-0 overflow-hidden relative">
                              {(() => {
                                const primaryImage = item.product.image ? { url: item.product.image } : null
                                return primaryImage ? (
                                  <img
                                    src={primaryImage.url}
                                    alt={item.product.name}
                                    className="w-full h-full object-cover"
                                    style={{ width: '40px', height: '48px' }}
                                    onError={(e) => {
                                      const target = e.target as HTMLImageElement
                                      target.style.display = 'none'
                                      target.nextElementSibling?.classList.remove('hidden')
                                    }}
                                  />
                                ) : null
                              })()}
                              <div className={`absolute inset-0 flex items-center justify-center ${item.product.image ? 'hidden' : ''}`}>
                                <Package className="h-3 w-3 text-gray-400" />
                              </div>
                            </div>

                            {/* Product Info & Controls */}
                            <div className="flex-1 min-w-0">
                              <div className="flex items-center justify-between">
                                <div className="flex-1 min-w-0">
                                  <h4 className="font-medium text-xs text-gray-900 dark:text-white truncate">
                                    {item.product.name}
                                  </h4>
                                  <p className="text-xs text-gray-500 dark:text-gray-400">
                                    SKU: {item.product.sku}
                                  </p>
                                </div>

                                {/* Price */}
                                <div className="text-right mr-2">
                                  <p className="text-xs font-bold text-blue-600 dark:text-blue-400">
                                    {formatCurrency(item.totalPrice)}
                                  </p>
                                  <p className="text-xs text-gray-500 dark:text-gray-400">
                                    {formatCurrency(item.product.price)} each
                                  </p>
                                </div>
                              </div>

                              {/* Quantity Controls Row - Compact */}
                              <div className="mt-1 flex items-center justify-between">
                                <div className="flex items-center space-x-1 bg-gray-50 dark:bg-gray-700/50 rounded-md p-1">
                                  <Tooltip>
                                    <TooltipTrigger asChild>
                                      <Button
                                        variant="ghost"
                                        size="sm"
                                        onClick={() => updateQuantity(item.product._id, item.quantity - 1)}
                                        className="h-5 w-5 p-0 hover:bg-gray-200 dark:hover:bg-gray-600 rounded transition-all duration-300 hover:scale-110"
                                      >
                                        <Minus className="h-2 w-2" />
                                      </Button>
                                    </TooltipTrigger>
                                    <TooltipContent>
                                      <p>Decrease quantity</p>
                                    </TooltipContent>
                                  </Tooltip>

                                  <span className="text-xs font-bold w-6 text-center text-gray-900 dark:text-white bg-white dark:bg-gray-800 rounded px-1 py-0.5">
                                    {item.quantity}
                                  </span>

                                  <Tooltip>
                                    <TooltipTrigger asChild>
                                      <Button
                                        variant="ghost"
                                        size="sm"
                                        onClick={() => updateQuantity(item.product._id, item.quantity + 1)}
                                        className="h-5 w-5 p-0 hover:bg-gray-200 dark:hover:bg-gray-600 rounded transition-all duration-300 hover:scale-110"
                                        disabled={item.quantity >= item.product.inventory.quantity}
                                      >
                                        <Plus className="h-2 w-2" />
                                      </Button>
                                    </TooltipTrigger>
                                    <TooltipContent>
                                      <p>Increase quantity</p>
                                    </TooltipContent>
                                  </Tooltip>
                                </div>

                                {/* Remove Button */}
                                <Tooltip>
                                  <TooltipTrigger asChild>
                                    <Button
                                      variant="ghost"
                                      size="sm"
                                      onClick={() => removeFromCart(item.product._id)}
                                      className="h-5 w-5 p-0 text-red-500 hover:text-red-700 hover:bg-red-50 dark:hover:bg-red-900/20 rounded flex-shrink-0 transition-all duration-300 hover:scale-110"
                                    >
                                      <Trash2 className="h-2 w-2" />
                                    </Button>
                                  </TooltipTrigger>
                                  <TooltipContent>
                                    <p>Remove item</p>
                                  </TooltipContent>
                                </Tooltip>
                              </div>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>

                {/* Cart Footer - Compact */}
                {cart.length > 0 && (
                  <div className="bg-gradient-to-r from-gray-50 to-gray-100 dark:from-gray-800 dark:to-gray-900 p-3 border-t border-gray-200 dark:border-gray-700">

                  {/* Order Summary - Compact */}
                  <div className="bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm rounded-lg p-3 mb-3 shadow-lg border border-gray-200 dark:border-gray-700">
                    <div className="flex items-center space-x-2 mb-2">
                      <Calculator className="h-4 w-4 text-blue-600 dark:text-blue-400" />
                      <h3 className="font-semibold text-sm text-gray-900 dark:text-white">
                        {language === 'mm' ? 'အော်ဒါအကျဉ်း' : 'Order Summary'}
                      </h3>
                    </div>

                    <div className="space-y-2">
                      {/* Subtotal */}
                      <div className="flex justify-between items-center py-1">
                        <span className="text-xs font-medium text-gray-600 dark:text-gray-400">
                          {language === 'mm' ? 'ခွဲစုစုပေါင်း' : 'Subtotal'}:
                        </span>
                        <span className="font-semibold text-sm text-gray-900 dark:text-white">
                          {formatCurrency(getSubtotal())}
                        </span>
                      </div>

                      {/* Tax */}
                      <div className="flex justify-between items-center py-1 bg-gray-50 dark:bg-gray-700/50 rounded-md px-2">
                        <div className="flex items-center space-x-1">
                          <Percent className="h-3 w-3 text-green-500" />
                          <span className="text-xs font-medium text-gray-600 dark:text-gray-400">
                            {t.tax}:
                          </span>
                        </div>
                        <div className="flex items-center space-x-1">
                          <Input
                            type="number"
                            value={taxRate}
                            onChange={(e) => setTaxRate(Number(e.target.value))}
                            className="w-12 h-6 text-xs text-center border-gray-300 dark:border-gray-600 rounded"
                            min="0"
                            max="100"
                          />
                          <span className="text-xs text-gray-500">%</span>
                          <span className="font-semibold text-green-600 dark:text-green-400 min-w-[50px] text-right text-xs">
                            {formatCurrency(getTaxAmount())}
                          </span>
                        </div>
                      </div>

                      {/* Discount */}
                      <div className="flex justify-between items-center py-1 bg-gray-50 dark:bg-gray-700/50 rounded-md px-2">
                        <div className="flex items-center space-x-1">
                          <Tag className="h-3 w-3 text-orange-500" />
                          <span className="text-xs font-medium text-gray-600 dark:text-gray-400">
                            {t.discount}:
                          </span>
                        </div>
                        <div className="flex items-center space-x-1">
                          <Input
                            type="number"
                            value={discountRate}
                            onChange={(e) => setDiscountRate(Number(e.target.value))}
                            className="w-12 h-6 text-xs text-center border-gray-300 dark:border-gray-600 rounded"
                            min="0"
                            max="100"
                          />
                          <span className="text-xs text-gray-500">%</span>
                          <span className="font-semibold text-orange-600 dark:text-orange-400 min-w-[50px] text-right text-xs">
                            -{formatCurrency(getDiscountAmount())}
                          </span>
                        </div>
                      </div>

                      {/* Shipping */}
                      <div className="flex justify-between items-center py-1 bg-gray-50 dark:bg-gray-700/50 rounded-md px-2">
                        <div className="flex items-center space-x-1">
                          <Truck className="h-3 w-3 text-purple-500" />
                          <span className="text-xs font-medium text-gray-600 dark:text-gray-400">
                            {t.shipping}:
                          </span>
                        </div>
                        <div className="flex items-center space-x-1">
                          <Input
                            type="number"
                            value={shippingCost}
                            onChange={(e) => setShippingCost(Number(e.target.value))}
                            className="w-14 h-6 text-xs text-center border-gray-300 dark:border-gray-600 rounded"
                            min="0"
                          />
                          <span className="font-semibold text-purple-600 dark:text-purple-400 min-w-[50px] text-right text-xs">
                            {formatCurrency(getShippingAmount())}
                          </span>
                        </div>
                      </div>

                      {/* Total */}
                      <div className="border-t border-gray-200 dark:border-gray-600 pt-2">
                        <div className="flex justify-between items-center bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-900/20 dark:to-purple-900/20 rounded-md p-2">
                          <span className="text-sm font-bold text-gray-900 dark:text-white">
                            {t.total}:
                          </span>
                          <span className="text-lg font-bold text-blue-600 dark:text-blue-400">
                            {formatCurrency(getTotalAmount())}
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Action Buttons - Compact */}
                  <div className="space-y-1.5">
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <Button
                          onClick={() => {
                            if (selectedPaymentMethod && selectedCustomer && cart.length > 0 && isPaymentDataComplete()) {
                              setShowPaymentModal(true)
                            }
                          }}
                          disabled={isProcessing || !selectedCustomer || !selectedPaymentMethod || cart.length === 0 || !isPaymentDataComplete()}
                          size="sm"
                          className="w-full h-10 bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white border-0 shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105 group relative overflow-hidden text-sm"
                        >
                          <div className="absolute inset-0 bg-gradient-to-r from-white/0 via-white/20 to-white/0 transform -skew-x-12 -translate-x-full group-hover:translate-x-full transition-transform duration-1000"></div>
                          {isProcessing ? (
                            <>
                              <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                              <span className="font-medium">{t.processing}</span>
                            </>
                          ) : (
                            <>
                              <CreditCard className="h-4 w-4 mr-2 group-hover:animate-bounce" />
                              <span className="font-medium">
                                {language === 'mm' ? 'ငွေချေရန်' : 'Proceed to Payment'}
                              </span>
                            </>
                          )}
                        </Button>
                      </TooltipTrigger>
                      <TooltipContent>
                        <p>
                          {!selectedCustomer ? 'Please select a customer first' :
                           !selectedPaymentMethod ? 'Please select a payment method' :
                           cart.length === 0 ? 'Please add items to cart' :
                           !isPaymentDataComplete() ? 'Please fill in payment details' :
                           'Process payment'}
                        </p>
                      </TooltipContent>
                    </Tooltip>

                    <div className="flex space-x-1.5">
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <Button
                            variant="outline"
                            onClick={clearCart}
                            size="sm"
                            className="flex-1 h-8 border-red-200 text-red-600 hover:bg-red-50 dark:border-red-800 dark:text-red-400 dark:hover:bg-red-900/20 transition-all duration-300 hover:scale-105 group text-xs"
                          >
                            <Trash2 className="h-3 w-3 mr-1 group-hover:animate-bounce" />
                            <span className="font-medium">{t.clearCart}</span>
                          </Button>
                        </TooltipTrigger>
                        <TooltipContent>
                          <p>Clear all items from cart</p>
                        </TooltipContent>
                      </Tooltip>
                    </div>

                    {/* Payment Method Section - Only show when customer and items exist */}
                    {selectedCustomer?.name && cart.length > 0 && (
                      <div className="mt-4 p-4 bg-gray-50 dark:bg-gray-800/50 rounded-lg border border-gray-200 dark:border-gray-700">
                        <div className="flex items-center gap-2 mb-3">
                          <CreditCard className="h-4 w-4 text-blue-500" />
                          <h3 className="font-medium text-gray-900 dark:text-white">
                            {t.paymentMethod}
                          </h3>
                        </div>

                        {/* Payment Method Grid */}
                        <div className="grid grid-cols-3 gap-2 mb-4">
                          {paymentMethods.map((method) => (
                            <button
                              type="button"
                              key={method.id}
                              onClick={() => setSelectedPaymentMethod(method.id)}
                              className={`p-3 rounded-lg border-2 transition-all duration-200 hover:scale-105 ${
                                selectedPaymentMethod === method.id
                                  ? 'border-green-500 bg-green-50 dark:bg-green-900/20'
                                  : 'border-gray-200 dark:border-gray-600 hover:border-gray-300 dark:hover:border-gray-500'
                              }`}
                            >
                              <div className={`w-8 h-8 rounded-full ${method.color} flex items-center justify-center mx-auto mb-1`}>
                                <method.icon className="h-4 w-4 text-white" />
                              </div>
                              <p className="text-xs text-center text-gray-700 dark:text-gray-300 font-medium">
                                {method.name}
                              </p>
                            </button>
                          ))}
                        </div>

                        {/* Selected Payment Method Details */}
                        {selectedPaymentMethod && (
                          <div className="p-3 bg-green-50 dark:bg-green-900/20 rounded-lg border border-green-200 dark:border-green-800">
                            <div className="flex items-center gap-2 mb-2">
                              <Check className="h-4 w-4 text-green-600" />
                              <span className="text-sm font-medium text-green-800 dark:text-green-400">
                                {language === 'mm' ? 'ရွေးချယ်ထားသည်' : 'Selected'}: {paymentMethods.find(m => m.id === selectedPaymentMethod)?.name}
                              </span>
                            </div>

                            {/* Payment Details Form */}
                            {!isPaymentDataComplete() && (
                              <div className="text-xs text-orange-600 dark:text-orange-400 mb-2 flex items-center gap-1">
                                <AlertCircle className="h-3 w-3" />
                                {language === 'mm' ? 'ငွေပေးချေမှုအချက်အလက်များ ဖြည့်သွင်းပါ' : 'Please fill in payment details'}
                              </div>
                            )}
                            {/* Dynamic Payment Fields Based on Selected Method */}
                            {selectedPaymentMethod === 'kbz' && (
                              <div className="space-y-2">
                                <div>
                                  <label className="text-xs text-gray-600 dark:text-gray-400">Phone Number</label>
                                  <input
                                    type="text"
                                    placeholder="09xxxxxxxxx"
                                    value={paymentData.phoneNumber}
                                    onChange={(e) => setPaymentData(prev => ({...prev, phoneNumber: e.target.value}))}
                                    className="w-full px-2 py-1 text-xs border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                                  />
                                </div>
                                <div>
                                  <label className="text-xs text-gray-600 dark:text-gray-400">Account Name</label>
                                  <input
                                    type="text"
                                    placeholder="Account holder name"
                                    value={paymentData.accountName}
                                    onChange={(e) => setPaymentData(prev => ({...prev, accountName: e.target.value}))}
                                    className="w-full px-2 py-1 text-xs border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                                  />
                                </div>
                                <div>
                                  <label className="text-xs text-gray-600 dark:text-gray-400">QR Code / Transaction ID</label>
                                  <input
                                    type="text"
                                    placeholder="KBZ123456789 or scan QR code"
                                    value={paymentData.qrCode}
                                    onChange={(e) => setPaymentData(prev => ({...prev, qrCode: e.target.value}))}
                                    className="w-full px-2 py-1 text-xs border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                                  />
                                </div>
                              </div>
                            )}

                            {selectedPaymentMethod === 'wave' && (
                              <div className="space-y-2">
                                <div>
                                  <label className="text-xs text-gray-600 dark:text-gray-400">Phone Number</label>
                                  <input
                                    type="text"
                                    placeholder="09xxxxxxxxx"
                                    value={paymentData.phoneNumber}
                                    onChange={(e) => setPaymentData(prev => ({...prev, phoneNumber: e.target.value}))}
                                    className="w-full px-2 py-1 text-xs border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                                  />
                                </div>
                                <div>
                                  <label className="text-xs text-gray-600 dark:text-gray-400">Account Name</label>
                                  <input
                                    type="text"
                                    placeholder="Account holder name"
                                    value={paymentData.accountName}
                                    onChange={(e) => setPaymentData(prev => ({...prev, accountName: e.target.value}))}
                                    className="w-full px-2 py-1 text-xs border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                                  />
                                </div>
                                <div>
                                  <label className="text-xs text-gray-600 dark:text-gray-400">QR Code / Transaction ID</label>
                                  <input
                                    type="text"
                                    placeholder="WM987654321 or scan QR code"
                                    value={paymentData.qrCode}
                                    onChange={(e) => setPaymentData(prev => ({...prev, qrCode: e.target.value}))}
                                    className="w-full px-2 py-1 text-xs border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                                  />
                                </div>
                              </div>
                            )}

                            {selectedPaymentMethod === 'nug' && (
                              <div className="space-y-2">
                                <div>
                                  <label className="text-xs text-gray-600 dark:text-gray-400">Phone Number</label>
                                  <input
                                    type="text"
                                    placeholder="09xxxxxxxxx"
                                    value={paymentData.phoneNumber}
                                    onChange={(e) => setPaymentData(prev => ({...prev, phoneNumber: e.target.value}))}
                                    className="w-full px-2 py-1 text-xs border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                                  />
                                </div>
                                <div>
                                  <label className="text-xs text-gray-600 dark:text-gray-400">Account Name</label>
                                  <input
                                    type="text"
                                    placeholder="Account holder name"
                                    value={paymentData.accountName}
                                    onChange={(e) => setPaymentData(prev => ({...prev, accountName: e.target.value}))}
                                    className="w-full px-2 py-1 text-xs border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                                  />
                                </div>
                                <div>
                                  <label className="text-xs text-gray-600 dark:text-gray-400">QR Code / Transaction ID</label>
                                  <input
                                    type="text"
                                    placeholder="NUG456789123 or scan QR code"
                                    value={paymentData.qrCode}
                                    onChange={(e) => setPaymentData(prev => ({...prev, qrCode: e.target.value}))}
                                    className="w-full px-2 py-1 text-xs border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                                  />
                                </div>
                              </div>
                            )}

                            {selectedPaymentMethod === 'bank' && (
                              <div className="space-y-2">
                                <div>
                                  <label className="text-xs text-gray-600 dark:text-gray-400">Bank Name</label>
                                  <select
                                    value={paymentData.bankName}
                                    onChange={(e) => setPaymentData(prev => ({...prev, bankName: e.target.value}))}
                                    className="w-full px-2 py-1 text-xs border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                                    title="Select Bank"
                                  >
                                    <option value="">Select Bank</option>
                                    {bankOptions.map((bank) => (
                                      <option key={bank} value={bank}>{bank}</option>
                                    ))}
                                  </select>
                                </div>
                                <div>
                                  <label className="text-xs text-gray-600 dark:text-gray-400">Account Number</label>
                                  <input
                                    type="text"
                                    placeholder="xxx-x-xxxxx-x"
                                    value={paymentData.accountNumber}
                                    onChange={(e) => setPaymentData(prev => ({...prev, accountNumber: e.target.value}))}
                                    className="w-full px-2 py-1 text-xs border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                                  />
                                </div>
                                <div>
                                  <label className="text-xs text-gray-600 dark:text-gray-400">Account Holder Name</label>
                                  <input
                                    type="text"
                                    placeholder="Account holder name"
                                    value={paymentData.accountHolderName}
                                    onChange={(e) => setPaymentData(prev => ({...prev, accountHolderName: e.target.value}))}
                                    className="w-full px-2 py-1 text-xs border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                                  />
                                </div>
                              </div>
                            )}

                            {selectedPaymentMethod === 'card' && (
                              <div className="space-y-2">
                                <div>
                                  <label className="text-xs text-gray-600 dark:text-gray-400">Card Number</label>
                                  <input
                                    type="text"
                                    placeholder="**** **** **** 1234"
                                    value={paymentData.cardNumber}
                                    onChange={(e) => setPaymentData(prev => ({...prev, cardNumber: e.target.value}))}
                                    className="w-full px-2 py-1 text-xs border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                                  />
                                </div>
                                <div>
                                  <label className="text-xs text-gray-600 dark:text-gray-400">Cardholder Name</label>
                                  <input
                                    type="text"
                                    placeholder="Mike Johnson"
                                    value={paymentData.cardholderName}
                                    onChange={(e) => setPaymentData(prev => ({...prev, cardholderName: e.target.value}))}
                                    className="w-full px-2 py-1 text-xs border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                                  />
                                </div>
                                <div>
                                  <label className="text-xs text-gray-600 dark:text-gray-400">Transaction ID / Approval Code</label>
                                  <input
                                    type="text"
                                    placeholder="TXN789012345"
                                    value={paymentData.transactionId}
                                    onChange={(e) => setPaymentData(prev => ({...prev, transactionId: e.target.value}))}
                                    className="w-full px-2 py-1 text-xs border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                                  />
                                </div>
                              </div>
                            )}

                            {selectedPaymentMethod === 'cash' && (
                              <div className="text-center py-4">
                                <Coins className="h-8 w-8 text-green-500 mx-auto mb-2" />
                                <p className="text-sm text-gray-600 dark:text-gray-400">Cash Payment</p>
                                <p className="text-xs text-gray-500 dark:text-gray-500">No additional details required</p>
                              </div>
                            )}
                          </div>
                        )}
                      </div>
                    )}
                  </div>
                  </div>
                )}
              </div>
            </aside>
          )}
        </div>

        {/* Mobile Cart Modal */}
        {isCartVisible && (
          <div className="lg:hidden fixed inset-0 bg-black/60 backdrop-blur-sm flex items-center justify-center z-50 p-2">
            <div className="bg-white dark:bg-gray-900 rounded-2xl shadow-2xl w-full h-full max-w-lg border border-gray-200 dark:border-gray-700 flex flex-col overflow-hidden">
              {/* Mobile Cart Header */}
              <div className="flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700 bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-900/20 dark:to-purple-900/20 rounded-t-2xl">
                <div className="flex items-center space-x-2">
                  <ShoppingCart className="h-5 w-5 text-blue-600" />
                  <h2 className="text-lg font-semibold text-gray-900 dark:text-white">
                    {t.cart}
                  </h2>
                </div>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setIsCartVisible(false)}
                  className="h-8 w-8 p-0"
                >
                  <X className="h-5 w-5" />
                </Button>
              </div>

              {/* Mobile Cart Content - Same as Desktop */}
              <div className="flex-1 overflow-y-auto p-4">
                {cart.length === 0 ? (
                  <div className="text-center py-16">
                    <div className="w-20 h-20 bg-gradient-to-br from-gray-100 to-gray-200 dark:from-gray-700 dark:to-gray-800 rounded-full flex items-center justify-center mx-auto mb-4 animate-pulse">
                      <ShoppingCart className="h-10 w-10 text-gray-400" />
                    </div>
                    <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
                      {language === 'mm' ? 'ခြင်းတောင်းအလွတ်' : 'Empty Cart'}
                    </h3>
                    <p className="text-gray-500 dark:text-gray-400 text-sm">
                      {language === 'mm' ? 'ကုန်ပစ္စည်းများကို ရွေးချယ်ပါ' : 'Add products to get started'}
                    </p>
                  </div>
                ) : (
                  <div className="space-y-3">
                    {cart.map((item, index) => (
                      <div
                        key={item.product._id}
                        className="bg-white/90 dark:bg-gray-800/90 backdrop-blur-sm border border-gray-200 dark:border-gray-700 rounded-xl p-3 hover:shadow-lg transition-all duration-300"
                      >
                        {/* Product Info Row */}
                        <div className="flex items-center space-x-3">
                          <div className="w-6 h-6 bg-blue-100 dark:bg-blue-900/30 rounded-full flex items-center justify-center flex-shrink-0">
                            <span className="text-xs font-bold text-blue-600 dark:text-blue-400">
                              {index + 1}
                            </span>
                          </div>
                          <div className="w-8 h-8 bg-gradient-to-br from-gray-100 to-gray-200 dark:from-gray-700 dark:to-gray-800 rounded-lg flex items-center justify-center flex-shrink-0">
                            <Package className="h-4 w-4 text-gray-400" />
                          </div>
                          <div className="flex-1 min-w-0">
                            <h4 className="font-semibold text-sm text-gray-900 dark:text-white truncate">
                              {item.product.name}
                            </h4>
                            <p className="text-xs text-gray-500 dark:text-gray-400">
                              SKU: {item.product.sku}
                            </p>
                          </div>
                          <div className="text-right">
                            <p className="text-sm font-bold text-blue-600 dark:text-blue-400">
                              {formatCurrency(item.totalPrice)}
                            </p>
                            <p className="text-xs text-gray-500 dark:text-gray-400">
                              {formatCurrency(item.product.price)} each
                            </p>
                          </div>
                        </div>

                        {/* Quantity Controls Row */}
                        <div className="mt-3 flex items-center justify-between">
                          <div className="flex items-center space-x-2 bg-gray-50 dark:bg-gray-700/50 rounded-lg p-1">
                            <Tooltip>
                              <TooltipTrigger asChild>
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => updateQuantity(item.product._id, item.quantity - 1)}
                                  className="h-6 w-6 p-0 hover:bg-gray-200 dark:hover:bg-gray-600 rounded-md transition-all duration-300 hover:scale-110"
                                >
                                  <Minus className="h-3 w-3" />
                                </Button>
                              </TooltipTrigger>
                              <TooltipContent>
                                <p>Decrease quantity</p>
                              </TooltipContent>
                            </Tooltip>

                            <span className="text-sm font-bold w-8 text-center text-gray-900 dark:text-white bg-white dark:bg-gray-800 rounded px-2 py-1">
                              {item.quantity}
                            </span>

                            <Tooltip>
                              <TooltipTrigger asChild>
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => updateQuantity(item.product._id, item.quantity + 1)}
                                  className="h-6 w-6 p-0 hover:bg-gray-200 dark:hover:bg-gray-600 rounded-md transition-all duration-300 hover:scale-110"
                                  disabled={item.quantity >= item.product.inventory.quantity}
                                >
                                  <Plus className="h-3 w-3" />
                                </Button>
                              </TooltipTrigger>
                              <TooltipContent>
                                <p>Increase quantity</p>
                              </TooltipContent>
                            </Tooltip>
                          </div>

                          {/* Remove Button */}
                          <Tooltip>
                            <TooltipTrigger asChild>
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => removeFromCart(item.product._id)}
                                className="h-6 w-6 p-0 text-red-500 hover:text-red-700 hover:bg-red-50 dark:hover:bg-red-900/20 rounded-lg flex-shrink-0 transition-all duration-300 hover:scale-110"
                              >
                                <Trash2 className="h-3 w-3" />
                              </Button>
                            </TooltipTrigger>
                            <TooltipContent>
                              <p>Remove item</p>
                            </TooltipContent>
                          </Tooltip>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </div>

              {/* Mobile Cart Footer */}
              {cart.length > 0 && (
                <div className="bg-gradient-to-r from-gray-50 to-gray-100 dark:from-gray-800 dark:to-gray-900 p-4 border-t border-gray-200 dark:border-gray-700 space-y-4">


                  {/* Total Section */}
                  <div className="flex justify-between items-center">
                    <span className="text-lg font-bold text-gray-900 dark:text-white">
                      {t.total}:
                    </span>
                    <span className="text-2xl font-bold text-blue-600 dark:text-blue-400">
                      {formatCurrency(getTotalAmount())}
                    </span>
                  </div>

                  {/* Checkout Button */}
                  <Button
                    onClick={processPayment}
                    disabled={isProcessing || !selectedCustomer}
                    size="lg"
                    className="w-full h-12 bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white"
                  >
                    {isProcessing ? (
                      <>
                        <RefreshCw className="h-5 w-5 mr-3 animate-spin" />
                        <span>{t.processing}</span>
                      </>
                    ) : (
                      <>
                        <CreditCard className="h-5 w-5 mr-3" />
                        <span>{language === 'mm' ? 'ငွေချေရန်' : 'Checkout'}</span>
                      </>
                    )}
                  </Button>
                </div>
              )}
            </div>
          </div>
        )}



        {/* Payment Modal */}
        {showPaymentModal && selectedPaymentMethod && (
          <div className="fixed inset-0 bg-black/60 backdrop-blur-sm flex items-center justify-center z-50 p-4">
            <div className="bg-gray-800 rounded-xl shadow-2xl w-full max-w-4xl mx-4 border border-gray-700">
              {/* Header */}
              <div className="flex items-center justify-between p-4 border-b border-gray-700">
                <div className="flex items-center space-x-2">
                  <CreditCard className="h-5 w-5 text-blue-400" />
                  <h3 className="text-lg font-bold text-white">
                    {language === 'mm' ? 'ငွေချေမှု အတည်ပြုရန်' : 'Payment Verification'}
                  </h3>
                </div>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setShowPaymentModal(false)}
                  className="h-8 w-8 p-0 text-gray-400 hover:text-white"
                >
                  <X className="h-4 w-4" />
                </Button>
              </div>

              {/* Content - 3 Column Layout */}
              <div className="p-6 grid grid-cols-3 gap-6">
                {/* Left Column - Customer Info */}
                <div className="space-y-4">
                  {selectedCustomer && (
                    <div className="bg-gray-700 rounded-lg p-4">
                      <div className="flex items-center space-x-2 mb-3">
                        <User className="h-4 w-4 text-blue-400" />
                        <h4 className="text-sm font-semibold text-white">
                          {language === 'mm' ? 'ဖောက်သည်' : 'Customer'}
                        </h4>
                      </div>
                      <div className="space-y-2 text-sm">
                        <div>
                          <span className="text-gray-400 block">{language === 'mm' ? 'နာမည်:' : 'Name:'}</span>
                          <span className="text-white font-medium">{selectedCustomer.name}</span>
                        </div>
                        <div>
                          <span className="text-gray-400 block">{language === 'mm' ? 'လိပ်စာ:' : 'Address:'}</span>
                          <span className="text-white">{typeof selectedCustomer.address === 'string'
                            ? selectedCustomer.address
                            : (selectedCustomer.address && typeof selectedCustomer.address === 'object' && 'street' in selectedCustomer.address
                                ? (selectedCustomer.address as any).street
                                : ''
                              )}</span>
                        </div>
                        <div>
                          <span className="text-gray-400 block">{language === 'mm' ? 'ဖုန်း:' : 'Phone:'}</span>
                          <span className="text-white">{selectedCustomer.phone}</span>
                        </div>
                      </div>
                    </div>
                  )}
                </div>

                {/* Middle Column - Edit Payment & Security */}
                <div className="space-y-4">
                  {/* Edit Payment Data */}
                  <div className="bg-gray-700 rounded-lg p-4">
                    <h4 className="text-sm font-semibold text-white mb-3">
                      {language === 'mm' ? 'ငွေချေမှု အချက်အလက်များ ပြင်ဆင်ရန်' : 'Edit payment details if needed'}
                    </h4>
                    <Button
                      variant="outline"
                      onClick={() => setShowPaymentModal(false)}
                      className="w-full border-gray-600 text-gray-300 hover:bg-gray-600 h-10"
                    >
                      <Edit className="h-4 w-4 mr-2" />
                      {language === 'mm' ? 'အချက်အလက်ပြင်ရန်' : 'Edit Payment Info'}
                    </Button>
                  </div>

                  {/* Security Verification */}
                  <div className="bg-gray-700 rounded-lg p-4">
                    <div className="flex items-center space-x-2 mb-3">
                      <AlertCircle className="h-4 w-4 text-orange-400" />
                      <h4 className="text-sm font-semibold text-white">
                        {language === 'mm' ? 'လုံခြုံရေး အတည်ပြုမှု' : 'Security Verification'}
                      </h4>
                    </div>
                    <input
                      type="password"
                      placeholder={language === 'mm' ? 'စကားဝှက်ထည့်ပါ' : 'Enter password'}
                      value={verificationPassword}
                      onChange={(e) => setVerificationPassword(e.target.value)}
                      className="w-full bg-gray-600 border border-gray-500 text-white placeholder-gray-400 h-10 rounded px-3 mb-3"
                      onKeyDown={(e) => {
                        if (e.key === 'Enter' && verificationPassword === 'admin123') {
                          processPayment()
                        }
                      }}
                    />
                    <Button
                      onClick={() => {
                        if (verificationPassword === 'admin123') {
                          processPayment()
                        } else {
                          alert('Invalid password!')
                        }
                      }}
                      disabled={!verificationPassword}
                      className="w-full bg-green-600 hover:bg-green-700 text-white h-10"
                    >
                      <Check className="h-4 w-4 mr-2" />
                      {language === 'mm' ? 'အတည်ပြုပြီး ငွေချေရန်' : 'Verify & Process Payment'}
                    </Button>
                  </div>
                </div>

                {/* Right Column - Payment Method Info */}
                <div className="space-y-4">
                  {/* Payment Info Card */}
                  <div className="bg-gray-700 rounded-lg p-4 space-y-3">
                    <div className="flex items-center space-x-2 mb-3">
                      <CreditCard className="h-4 w-4 text-blue-400" />
                      <h4 className="text-sm font-semibold text-white">
                        {language === 'mm' ? 'ငွေချေနည်း' : 'Method'}
                      </h4>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span className="text-gray-400">Method:</span>
                      <span className="text-white font-medium">
                        {paymentMethods.find(m => m.id === selectedPaymentMethod)?.name}
                      </span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span className="text-gray-400">Amount:</span>
                      <span className="text-green-400 font-bold text-base">
                        {formatCurrency(getTotalAmount())}
                      </span>
                    </div>
                  {/* Dynamic Payment Info Display */}
                  {(selectedPaymentMethod === 'kbz' || selectedPaymentMethod === 'wave' || selectedPaymentMethod === 'nug') && (
                    <>
                      {paymentData.phoneNumber && (
                        <div className="flex justify-between text-sm">
                          <span className="text-gray-400">Phone:</span>
                          <span className="text-white">{paymentData.phoneNumber}</span>
                        </div>
                      )}
                      {paymentData.accountName && (
                        <div className="flex justify-between text-sm">
                          <span className="text-gray-400">Name:</span>
                          <span className="text-white">{paymentData.accountName}</span>
                        </div>
                      )}
                      {paymentData.qrCode && (
                        <div className="flex justify-between text-sm">
                          <span className="text-gray-400">QR/Transaction:</span>
                          <span className="text-white">{paymentData.qrCode}</span>
                        </div>
                      )}
                    </>
                  )}

                  {selectedPaymentMethod === 'bank' && (
                    <>
                      {paymentData.bankName && (
                        <div className="flex justify-between text-sm">
                          <span className="text-gray-400">Bank:</span>
                          <span className="text-white">{paymentData.bankName}</span>
                        </div>
                      )}
                      {paymentData.accountNumber && (
                        <div className="flex justify-between text-sm">
                          <span className="text-gray-400">Account:</span>
                          <span className="text-white">{paymentData.accountNumber}</span>
                        </div>
                      )}
                      {paymentData.accountHolderName && (
                        <div className="flex justify-between text-sm">
                          <span className="text-gray-400">Holder:</span>
                          <span className="text-white">{paymentData.accountHolderName}</span>
                        </div>
                      )}
                    </>
                  )}

                  {selectedPaymentMethod === 'card' && (
                    <>
                      {paymentData.cardNumber && (
                        <div className="flex justify-between text-sm">
                          <span className="text-gray-400">Card:</span>
                          <span className="text-white">{paymentData.cardNumber}</span>
                        </div>
                      )}
                      {paymentData.cardholderName && (
                        <div className="flex justify-between text-sm">
                          <span className="text-gray-400">Holder:</span>
                          <span className="text-white">{paymentData.cardholderName}</span>
                        </div>
                      )}
                      {paymentData.transactionId && (
                        <div className="flex justify-between text-sm">
                          <span className="text-gray-400">Transaction:</span>
                          <span className="text-white">{paymentData.transactionId}</span>
                        </div>
                      )}
                    </>
                  )}
                </div>

                  {/* QR Code for Digital Payments */}
                  {(selectedPaymentMethod === 'kbz' || selectedPaymentMethod === 'wave' || selectedPaymentMethod === 'nug') && (
                    <div className="bg-gray-700 rounded-lg p-4 text-center">
                      <h4 className="text-sm font-semibold text-white mb-3">
                        {language === 'mm' ? 'QR ကုဒ်' : 'QR Code'}
                      </h4>
                      <div className="w-32 h-32 bg-white rounded-lg mx-auto flex items-center justify-center border-2 border-blue-400">
                        <div className="text-center">
                          <QrCode className="h-12 w-12 text-blue-500 mx-auto mb-2" />
                          <p className="text-xs text-gray-600 font-medium">QR Code</p>
                        </div>
                      </div>
                      <p className="text-xs text-blue-400 mt-2 flex items-center justify-center">
                        <QrCode className="h-3 w-3 mr-1" />
                        {language === 'mm' ? 'အတည်ပြုရန် စကင်န်ဖတ်ပါ' : 'Scan for verification'}
                      </p>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        )}



        {/* Product Details Modal */}
        {isProductDetailsOpen && selectedProductDetails && (
          <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
            <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-2xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
              {/* Modal Header */}
              <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
                <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
                  {language === 'mm' ? 'ကုန်ပစ္စည်းအသေးစိတ်' : 'Product Details'}
                </h2>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setIsProductDetailsOpen(false)}
                  className="h-8 w-8 p-0 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700"
                >
                  <X className="h-5 w-5" />
                </Button>
              </div>

              {/* Modal Content */}
              <div className="p-6 space-y-6">
                {/* Product Image */}
                <div className="w-full h-64 bg-gradient-to-br from-gray-100 to-gray-200 dark:from-gray-700 dark:to-gray-800 rounded-xl flex items-center justify-center overflow-hidden">
                  {(() => {
                    const productImages = selectedProductDetails.images || []
                    const primaryImage = productImages.find((img: any) => img.isPrimary) || productImages[0]
                    const hasImage = primaryImage && primaryImage.url

                    return hasImage ? (
                      <img
                        src={primaryImage.url}
                        alt={primaryImage.alt || selectedProductDetails.name}
                        className="w-full h-full object-contain p-4"
                      />
                    ) : (
                      <div className="text-center">
                        <Package className="h-16 w-16 text-gray-400 mx-auto mb-2" />
                        <p className="text-gray-500 dark:text-gray-400">No Image Available</p>
                      </div>
                    )
                  })()}
                </div>

                {/* Product Information */}
                <div className="space-y-4">
                  {/* Product Name */}
                  <div>
                    <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
                      {selectedProductDetails.name}
                    </h3>
                    <Badge
                      variant="outline"
                      className="text-sm font-semibold px-3 py-1"
                      style={{
                        borderColor: selectedProductDetails.category?.color || '#6B7280',
                        color: selectedProductDetails.category?.color || '#6B7280',
                        backgroundColor: `${selectedProductDetails.category?.color || '#6B7280'}15`
                      }}
                    >
                      {selectedProductDetails.category?.name || 'General'}
                    </Badge>
                  </div>

                  {/* Price */}
                  <div className="bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-900/20 dark:to-purple-900/20 rounded-xl p-4">
                    <div className="flex items-center space-x-2 mb-2">
                      <DollarSign className="h-5 w-5 text-blue-600" />
                      <span className="text-sm font-medium text-gray-600 dark:text-gray-400">
                        {language === 'mm' ? 'စျေးနှုန်း' : 'Price'}
                      </span>
                    </div>
                    <span className="text-3xl font-bold text-blue-600 dark:text-blue-400">
                      {formatCurrency(selectedProductDetails.price)}
                    </span>
                  </div>

                  {/* Product Details Grid */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {/* SKU */}
                    <div className="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-4">
                      <div className="flex items-center space-x-2 mb-2">
                        <Barcode className="h-4 w-4 text-gray-600 dark:text-gray-400" />
                        <span className="text-sm font-medium text-gray-600 dark:text-gray-400">SKU</span>
                      </div>
                      <span className="text-lg font-semibold text-gray-900 dark:text-white">
                        {selectedProductDetails.sku || 'N/A'}
                      </span>
                    </div>

                    {/* Stock */}
                    <div className="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-4">
                      <div className="flex items-center space-x-2 mb-2">
                        <Package className="h-4 w-4 text-gray-600 dark:text-gray-400" />
                        <span className="text-sm font-medium text-gray-600 dark:text-gray-400">
                          {language === 'mm' ? 'စတော့' : 'Stock'}
                        </span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <div className={`w-3 h-3 rounded-full ${
                          selectedProductDetails.inventory?.quantity > 0 ? 'bg-green-500' : 'bg-red-500'
                        } animate-pulse`}></div>
                        <span className="text-lg font-semibold text-gray-900 dark:text-white">
                          {selectedProductDetails.inventory?.quantity || 0} {selectedProductDetails.inventory?.unit || 'pcs'}
                        </span>
                      </div>
                    </div>

                    {/* Status */}
                    <div className="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-4">
                      <div className="flex items-center space-x-2 mb-2">
                        <Tag className="h-4 w-4 text-gray-600 dark:text-gray-400" />
                        <span className="text-sm font-medium text-gray-600 dark:text-gray-400">
                          {language === 'mm' ? 'အခြေအနေ' : 'Status'}
                        </span>
                      </div>
                      <Badge
                        variant={selectedProductDetails.isActive ? 'default' : 'secondary'}
                        className={selectedProductDetails.isActive ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'}
                      >
                        {selectedProductDetails.isActive
                          ? (language === 'mm' ? 'ရရှိနိုင်သည်' : 'Active')
                          : (language === 'mm' ? 'မရရှိနိုင်ပါ' : 'Inactive')
                        }
                      </Badge>
                    </div>

                    {/* Created Date */}
                    <div className="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-4">
                      <div className="flex items-center space-x-2 mb-2">
                        <Calendar className="h-4 w-4 text-gray-600 dark:text-gray-400" />
                        <span className="text-sm font-medium text-gray-600 dark:text-gray-400">
                          {language === 'mm' ? 'ဖန်တီးသည့်ရက်' : 'Created'}
                        </span>
                      </div>
                      <span className="text-lg font-semibold text-gray-900 dark:text-white">
                        {selectedProductDetails.createdAt
                          ? new Date(selectedProductDetails.createdAt).toLocaleDateString()
                          : 'N/A'
                        }
                      </span>
                    </div>
                  </div>

                  {/* Description */}
                  {selectedProductDetails.description && (
                    <div className="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-4">
                      <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                        {language === 'mm' ? 'ဖော်ပြချက်' : 'Description'}
                      </h4>
                      <p className="text-gray-700 dark:text-gray-300 leading-relaxed">
                        {selectedProductDetails.description}
                      </p>
                    </div>
                  )}
                </div>

                {/* Action Buttons */}
                <div className="flex space-x-3 pt-4 border-t border-gray-200 dark:border-gray-700">
                  <Button
                    variant="outline"
                    onClick={() => setIsProductDetailsOpen(false)}
                    className="flex-1"
                  >
                    {language === 'mm' ? 'ပိတ်ရန်' : 'Close'}
                  </Button>
                  <Button
                    onClick={async () => {
                      await addToCart(selectedProductDetails)
                      setIsProductDetailsOpen(false)
                    }}
                    disabled={selectedProductDetails.inventory?.quantity === 0}
                    className="flex-1 bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700"
                  >
                    <ShoppingCart className="h-4 w-4 mr-2" />
                    {language === 'mm' ? 'ခြင်းတောင်းထဲထည့်ရန်' : 'Add to Cart'}
                  </Button>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Receipt Modal - Removed, now using separate invoice page */}
      </div>
    </TooltipProvider>
  )
}