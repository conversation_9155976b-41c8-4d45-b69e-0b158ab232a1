const express = require('express');
const { protect, authorize } = require('../middleware/auth');
const {
    getUsers,
    getUser,
    createUser,
    updateUser,
    deleteUser,
    updateUserStatus,
    changeUserPassword,
    getUserStats
} = require('../controllers/userController');

const router = express.Router();

// All routes require authentication
router.use(protect);

// User statistics (Admin, Manager)
router.get('/stats', authorize('admin', 'manager'), getUserStats);

// CRUD routes
router.route('/')
    .get(authorize('admin', 'manager'), getUsers)
    .post(authorize('admin', 'manager'), createUser);

router.route('/:id')
    .get(authorize('admin', 'manager'), getUser)
    .put(authorize('admin', 'manager'), updateUser)
    .delete(authorize('admin'), deleteUser);

// User status management
router.put('/:id/status', authorize('admin', 'manager'), updateUserStatus);

// Password management
router.put('/:id/password', authorize('admin'), changeUserPassword);

module.exports = router;
