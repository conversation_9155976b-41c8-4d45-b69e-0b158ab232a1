'use client'

import { useState, useEffect } from 'react'
import Link from 'next/link'
import { Button } from '@/components/ui/button'
import { useAuth } from '@/contexts/auth-context'
import { useTheme } from '@/contexts/theme-context'
import { useCurrency } from '@/contexts/currency-context'
import { useNotifications } from '@/contexts/notification-context'
import {
  Menu,
  X,
  User,
  Settings,
  LogOut,
  Sun,
  Moon,
  Globe,
  Bell,
  Clock,
  DollarSign,
  ChevronDown,
  Store,
  CreditCard,
  Receipt
} from 'lucide-react'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'

interface HeaderProps {
  language?: 'en' | 'mm'
  onNotificationClick?: () => void
  onMenuClick?: () => void
  onThemeClick?: () => void
}

export function Header({
  language = 'en',
  onNotificationClick,
  onMenuClick,
  onThemeClick
}: HeaderProps) {
  const [isMenuOpen, setIsMenuOpen] = useState(false)
  const [timeFormat, setTimeFormat] = useState<'12h' | '24h'>('12h')
  const [currentTime, setCurrentTime] = useState<Date | null>(null)
  const [mounted, setMounted] = useState(false)
  const [companyName, setCompanyName] = useState('BitesTech POS')
  const { logout } = useAuth()
  const { theme, toggleTheme, language: themeLanguage, toggleLanguage: themeToggleLanguage } = useTheme()
  const { currentCurrency, setCurrency, exchangeRates } = useCurrency()
  const { unreadCount } = useNotifications()

  // Initialize time on client side only
  useEffect(() => {
    setMounted(true)
    setCurrentTime(new Date())
    loadCompanyInfo()

    const timer = setInterval(() => {
      setCurrentTime(new Date())
    }, 1000)

    // Listen for storage changes to update company name in real-time
    const handleStorageChange = () => {
      loadCompanyInfo()
    }

    window.addEventListener('storage', handleStorageChange)
    window.addEventListener('companyInfoUpdated', handleStorageChange)

    return () => {
      clearInterval(timer)
      window.removeEventListener('storage', handleStorageChange)
      window.removeEventListener('companyInfoUpdated', handleStorageChange)
    }
  }, [])

  const loadCompanyInfo = () => {
    try {
      const stored = localStorage.getItem('bitstech_company_info')
      if (stored) {
        const parsed = JSON.parse(stored)
        setCompanyName(parsed.name || 'BitesTech POS')
      }
    } catch (error) {
      console.error('Error loading company info:', error)
    }
  }

  // Generate logo letter from company name
  const getLogoLetter = (name: string) => {
    return name.charAt(0).toUpperCase()
  }

  // Listen for global currency sync
  useEffect(() => {
    const handleCurrencySync = (event: CustomEvent) => {
      console.log('🔄 Header received currency sync:', event.detail.currency)
      // Header will automatically re-render with new currency from context
    }

    window.addEventListener('global-currency-sync', handleCurrencySync as EventListener)
    return () => {
      window.removeEventListener('global-currency-sync', handleCurrencySync as EventListener)
    }
  }, [])

  const toggleMenu = () => setIsMenuOpen(!isMenuOpen)
  const handleToggleLanguage = () => {
    // Only use theme context for language toggle to avoid conflicts
    themeToggleLanguage()
  }

  const handleLogout = async () => {
    try {
      await logout()
    } catch (error) {
      console.error('Logout failed:', error)
    }
  }

  return (
    <header className="fixed top-0 left-0 right-0 z-50 bg-white dark:bg-gray-900 shadow-sm border-b">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-16">
          {/* Logo */}
          <div className="flex items-center">
            <Link href="/" className="flex items-center space-x-2">
              <div className="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center">
                <span className="text-white font-bold text-sm">{getLogoLetter(companyName)}</span>
              </div>
              <span className="text-xl font-bold text-gray-900 dark:text-white">
                {companyName}
              </span>
            </Link>
          </div>

          {/* Desktop Navigation - POS Terminal, Time Format & Currency */}
          <nav className="hidden md:flex items-center space-x-6">
            {/* POS Terminal Link */}
            <div className="relative group">
              <Link href="/pos">
                <Button
                  variant="ghost"
                  size="sm"
                  className="text-gray-700 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 border border-gray-200 dark:border-gray-700 hover:border-blue-300 dark:hover:border-blue-600 transition-all duration-200"
                  title="POS Terminal"
                >
                  <Store className="h-4 w-4 mr-2" />
                  <span className="font-medium">
                    {themeLanguage === 'mm' ? 'ရောင်းချရေး' : 'POS Terminal'}
                  </span>
                </Button>
              </Link>
              <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-3 py-2 bg-gray-900 dark:bg-gray-100 text-white dark:text-gray-900 text-xs rounded-lg shadow-lg opacity-0 group-hover:opacity-100 transition-all duration-200 pointer-events-none whitespace-nowrap z-50">
                <div className="text-center">
                  <div className="font-semibold">
                    {themeLanguage === 'mm' ? 'ရောင်းချရေး စနစ်' : 'Point of Sale Terminal'}
                  </div>
                  <div className="text-xs mt-1 opacity-75">
                    {themeLanguage === 'mm' ? 'ရောင်းချရေး စနစ်သို့ သွားရန်' : 'Go to POS Terminal'}
                  </div>
                </div>
                <div className="absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-gray-900 dark:border-t-gray-100"></div>
              </div>
            </div>

            {/* Live Time Display */}
            <div className="relative group">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setTimeFormat(timeFormat === '12h' ? '24h' : '12h')}
                className="text-gray-700 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 font-mono"
                title={`Current time (${timeFormat === '12h' ? '12-hour' : '24-hour'} format)`}
              >
                <Clock className="h-4 w-4 mr-2" />
                {mounted && currentTime ? currentTime.toLocaleTimeString(themeLanguage === 'mm' ? 'my-MM' : 'en-US', {
                  hour: '2-digit',
                  minute: '2-digit',
                  second: '2-digit',
                  hour12: timeFormat === '12h'
                }) : '--:--:--'}
              </Button>
              <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-3 py-2 bg-gray-900 dark:bg-gray-100 text-white dark:text-gray-900 text-xs rounded-lg shadow-lg opacity-0 group-hover:opacity-100 transition-all duration-200 pointer-events-none whitespace-nowrap z-50">
                <div className="text-center">
                  <div className="font-semibold">
                    {mounted && currentTime ? currentTime.toLocaleDateString(themeLanguage === 'mm' ? 'my-MM' : 'en-US', {
                      weekday: 'long',
                      year: 'numeric',
                      month: 'long',
                      day: 'numeric'
                    }) : 'Loading...'}
                  </div>
                  <div className="text-xs mt-1 opacity-75">
                    Click to switch to {timeFormat === '12h' ? '24-hour' : '12-hour'} format
                  </div>
                </div>
                <div className="absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-gray-900 dark:border-t-gray-100"></div>
              </div>
            </div>

            {/* Quick Currency Icons */}
            <div className="flex items-center space-x-1 border border-gray-200 dark:border-gray-700 rounded-lg p-1">
              {/* MMK */}
              <div className="relative group">
                <Button
                  variant={currentCurrency === 'MMK' ? 'default' : 'ghost'}
                  size="sm"
                  onClick={() => setCurrency('MMK')}
                  className={`h-8 w-8 p-0 ${currentCurrency === 'MMK' ? 'bg-blue-600 text-white' : 'text-gray-700 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400'}`}
                  title="Myanmar Kyat"
                >
                  <span className="text-sm font-bold">K</span>
                </Button>
                <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-2 py-1 bg-black text-white text-xs rounded opacity-0 group-hover:opacity-100 transition-opacity pointer-events-none whitespace-nowrap z-50">
                  Myanmar Kyat
                </div>
              </div>

              {/* THB */}
              <div className="relative group">
                <Button
                  variant={currentCurrency === 'THB' ? 'default' : 'ghost'}
                  size="sm"
                  onClick={() => setCurrency('THB')}
                  className={`h-8 w-8 p-0 ${currentCurrency === 'THB' ? 'bg-blue-600 text-white' : 'text-gray-700 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400'}`}
                  title="Thai Baht"
                >
                  <span className="text-sm font-bold">฿</span>
                </Button>
                <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-2 py-1 bg-black text-white text-xs rounded opacity-0 group-hover:opacity-100 transition-opacity pointer-events-none whitespace-nowrap z-50">
                  Thai Baht
                </div>
              </div>

              {/* USD */}
              <div className="relative group">
                <Button
                  variant={currentCurrency === 'USD' ? 'default' : 'ghost'}
                  size="sm"
                  onClick={() => setCurrency('USD')}
                  className={`h-8 w-8 p-0 ${currentCurrency === 'USD' ? 'bg-blue-600 text-white' : 'text-gray-700 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400'}`}
                  title="US Dollar"
                >
                  <span className="text-sm font-bold">$</span>
                </Button>
                <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-2 py-1 bg-black text-white text-xs rounded opacity-0 group-hover:opacity-100 transition-opacity pointer-events-none whitespace-nowrap z-50">
                  US Dollar
                </div>
              </div>
            </div>

            {/* Enhanced Currency Selector */}
            <div className="relative group">
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="text-gray-700 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 border border-gray-200 dark:border-gray-700 hover:border-blue-300 dark:hover:border-blue-600 transition-all duration-200"
                    title="Select Currency"
                  >
                    <span className="mr-2 text-base">{exchangeRates[currentCurrency].flag}</span>
                    <span className="font-semibold">{exchangeRates[currentCurrency].symbol}</span>
                    <span className="ml-1 text-xs opacity-75">{currentCurrency}</span>
                    <ChevronDown className="h-3 w-3 ml-2" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end" className="w-64 p-2">
                  <div className="text-xs text-gray-500 dark:text-gray-400 mb-2 px-2">
                    Select Currency (Live Exchange Rates)
                  </div>
                  {Object.entries(exchangeRates).map(([code, data]) => (
                    <DropdownMenuItem
                      key={code}
                      onClick={() => setCurrency(code as any)}
                      className={`flex items-center justify-between p-3 rounded-lg ${
                        currentCurrency === code ? 'bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-700' : ''
                      }`}
                    >
                      <div className="flex items-center">
                        <span className="mr-3 text-lg">{data.flag}</span>
                        <div>
                          <div className="font-medium">{data.name}</div>
                          <div className="text-xs text-gray-500 dark:text-gray-400">{code}</div>
                        </div>
                      </div>
                      <div className="text-right">
                        <div className="font-mono text-sm">{data.symbol}</div>
                        <div className="text-xs text-gray-500 dark:text-gray-400">
                          {code === 'MMK' ? '1 MMK' : `1 ${code} = ${(1 / data.rate).toFixed(2)} MMK`}
                        </div>
                      </div>
                    </DropdownMenuItem>
                  ))}
                </DropdownMenuContent>
              </DropdownMenu>
              <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-3 py-2 bg-gray-900 dark:bg-gray-100 text-white dark:text-gray-900 text-xs rounded-lg shadow-lg opacity-0 group-hover:opacity-100 transition-all duration-200 pointer-events-none whitespace-nowrap z-50">
                <div className="text-center">
                  <div className="font-semibold">{exchangeRates[currentCurrency].name}</div>
                  <div className="text-xs mt-1 opacity-75">
                    Current: {exchangeRates[currentCurrency].symbol} {currentCurrency}
                  </div>
                </div>
                <div className="absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-gray-900 dark:border-t-gray-100"></div>
              </div>
            </div>
          </nav>

          {/* Right side buttons */}
          <div className="hidden md:flex items-center space-x-4">
            {/* Notification Bell */}
            {onNotificationClick && (
              <div className="relative group">
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={onNotificationClick}
                  className="text-gray-700 dark:text-gray-300 relative"
                  title="Notifications"
                >
                  <Bell className="h-5 w-5" />
                  {unreadCount > 0 && (
                    <span className="absolute -top-1 -right-1 h-4 w-4 bg-red-500 rounded-full text-xs text-white flex items-center justify-center">
                      {unreadCount > 9 ? '9+' : unreadCount}
                    </span>
                  )}
                </Button>
                <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-2 py-1 bg-black text-white text-xs rounded opacity-0 group-hover:opacity-100 transition-opacity pointer-events-none whitespace-nowrap">
                  Notifications
                </div>
              </div>
            )}

            {/* Language Toggle */}
            <div className="relative group">
              <Button
                variant="ghost"
                size="icon"
                onClick={handleToggleLanguage}
                className="text-gray-700 dark:text-gray-300"
                title="Toggle Language"
              >
                <Globe className="h-5 w-5" />
              </Button>
              <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-2 py-1 bg-black text-white text-xs rounded opacity-0 group-hover:opacity-100 transition-opacity pointer-events-none whitespace-nowrap">
                Toggle Language
              </div>
            </div>

            {/* Theme Toggle */}
            <div className="relative group">
              <Button
                variant="ghost"
                size="icon"
                onClick={onThemeClick || toggleTheme}
                className="text-gray-700 dark:text-gray-300"
                title="Toggle Theme"
              >
                {theme === 'dark' ? <Sun className="h-5 w-5" /> : <Moon className="h-5 w-5" />}
              </Button>
              <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-2 py-1 bg-black text-white text-xs rounded opacity-0 group-hover:opacity-100 transition-opacity pointer-events-none whitespace-nowrap">
                Toggle Theme
              </div>
            </div>

            {/* User Menu */}
            <div className="relative group">
              <Button variant="ghost" size="icon" title="User Profile">
                <User className="h-5 w-5" />
              </Button>
              <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-2 py-1 bg-black text-white text-xs rounded opacity-0 group-hover:opacity-100 transition-opacity pointer-events-none whitespace-nowrap">
                User Profile
              </div>
            </div>

            {/* Settings */}
            <div className="relative group">
              <Link href="/settings">
                <Button variant="ghost" size="icon" title="Settings">
                  <Settings className="h-5 w-5" />
                </Button>
              </Link>
              <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-2 py-1 bg-black text-white text-xs rounded opacity-0 group-hover:opacity-100 transition-opacity pointer-events-none whitespace-nowrap">
                Settings
              </div>
            </div>

            {/* Logout */}
            <div className="relative group">
              <Button
                variant="ghost"
                size="icon"
                className="text-red-600 hover:text-red-700"
                onClick={handleLogout}
                title="Logout"
              >
                <LogOut className="h-5 w-5" />
              </Button>
              <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-2 py-1 bg-black text-white text-xs rounded opacity-0 group-hover:opacity-100 transition-opacity pointer-events-none whitespace-nowrap">
                Logout
              </div>
            </div>
          </div>

          {/* Mobile menu button */}
          <div className="md:hidden">
            <Button
              variant="ghost"
              size="icon"
              onClick={onMenuClick || toggleMenu}
              className="text-gray-700 dark:text-gray-300"
            >
              {isMenuOpen ? <X className="h-6 w-6" /> : <Menu className="h-6 w-6" />}
            </Button>
          </div>
        </div>

        {/* Mobile Navigation */}
        {isMenuOpen && (
          <div className="md:hidden">
            <div className="px-2 pt-2 pb-3 space-y-1 sm:px-3 border-t">
              {/* Mobile POS Terminal Link */}
              <div className="px-3 py-2 border-b border-gray-200 dark:border-gray-700">
                <Link href="/pos">
                  <Button
                    variant="ghost"
                    size="sm"
                    className="w-full justify-start text-gray-700 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400"
                  >
                    <Store className="h-4 w-4 mr-2" />
                    {themeLanguage === 'mm' ? 'ရောင်းချရေး' : 'POS Terminal'}
                  </Button>
                </Link>
              </div>

              {/* Mobile Currency Icons */}
              <div className="px-3 py-2">
                <div className="text-xs text-gray-500 dark:text-gray-400 mb-2">
                  {themeLanguage === 'mm' ? 'ငွေကြေး' : 'Currency'}
                </div>
                <div className="flex items-center space-x-2">
                  <Button
                    variant={currentCurrency === 'MMK' ? 'default' : 'ghost'}
                    size="sm"
                    onClick={() => setCurrency('MMK')}
                    className={`h-8 w-12 ${currentCurrency === 'MMK' ? 'bg-blue-600 text-white' : 'text-gray-700 dark:text-gray-300'}`}
                  >
                    <span className="text-sm font-bold">K</span>
                  </Button>
                  <Button
                    variant={currentCurrency === 'THB' ? 'default' : 'ghost'}
                    size="sm"
                    onClick={() => setCurrency('THB')}
                    className={`h-8 w-12 ${currentCurrency === 'THB' ? 'bg-blue-600 text-white' : 'text-gray-700 dark:text-gray-300'}`}
                  >
                    <span className="text-sm font-bold">฿</span>
                  </Button>
                  <Button
                    variant={currentCurrency === 'USD' ? 'default' : 'ghost'}
                    size="sm"
                    onClick={() => setCurrency('USD')}
                    className={`h-8 w-12 ${currentCurrency === 'USD' ? 'bg-blue-600 text-white' : 'text-gray-700 dark:text-gray-300'}`}
                  >
                    <span className="text-sm font-bold">$</span>
                  </Button>
                </div>
              </div>

              {/* Mobile Time Format */}
              <div className="px-3 py-2">
                <div className="text-xs text-gray-500 dark:text-gray-400 mb-2">
                  {themeLanguage === 'mm' ? 'အချိန်' : 'Time Format'}
                </div>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setTimeFormat(timeFormat === '12h' ? '24h' : '12h')}
                  className="text-gray-700 dark:text-gray-300"
                >
                  <Clock className="h-4 w-4 mr-2" />
                  {timeFormat === '12h' ? '12H' : '24H'}
                </Button>
              </div>

              {/* Mobile action buttons */}
              <div className="flex items-center space-x-4 px-3 py-2 border-t">
                <Button variant="ghost" size="sm" onClick={handleToggleLanguage} title="Toggle Language">
                  <Globe className="h-4 w-4 mr-2" />
                  {themeLanguage === 'en' ? 'MM' : 'EN'}
                </Button>
                <Button variant="ghost" size="sm" onClick={onThemeClick || toggleTheme} title="Toggle Theme">
                  {theme === 'dark' ? <Sun className="h-4 w-4" /> : <Moon className="h-4 w-4" />}
                </Button>
                <Link href="/settings">
                  <Button variant="ghost" size="sm" title="Settings">
                    <Settings className="h-4 w-4" />
                  </Button>
                </Link>
                <Button variant="ghost" size="sm" className="text-red-600" onClick={handleLogout} title="Logout">
                  <LogOut className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </div>
        )}
      </div>
    </header>
  )
}
