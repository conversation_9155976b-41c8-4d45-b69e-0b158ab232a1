const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');
require('dotenv').config();

// Import models
const User = require('../models/User');
const Category = require('../models/Category');
const Product = require('../models/Product');

const connectDB = async () => {
    try {
        const mongoURI = process.env.MONGODB_URI || 'mongodb://localhost:27017/bitstech_pos';
        await mongoose.connect(mongoURI);
        console.log('✅ MongoDB connected for seeding');
    } catch (error) {
        console.error('❌ MongoDB connection failed:', error.message);
        process.exit(1);
    }
};

// Sample categories data
const categoriesData = [
    {
        name: 'Computers',
        nameMyanmar: 'ကွန်ပျူတာများ',
        description: 'Desktop computers, laptops, and workstations',
        color: '#3B82F6',
        icon: 'Monitor',
        isActive: true
    },
    {
        name: 'Components',
        nameMyanmar: 'အစိတ်အပိုင်းများ',
        description: 'Computer components and hardware',
        color: '#10B981',
        icon: 'HardDrive',
        isActive: true
    },
    {
        name: 'Peripherals',
        nameMyanmar: 'ပတ်ဝန်းကျင်ပစ္စည်းများ',
        description: 'Keyboards, mice, monitors, and other peripherals',
        color: '#F59E0B',
        icon: 'Keyboard',
        isActive: true
    },
    {
        name: 'Gaming',
        nameMyanmar: 'ဂိမ်းကစားရန်',
        description: 'Gaming accessories and equipment',
        color: '#EF4444',
        icon: 'Gamepad2',
        isActive: true
    },
    {
        name: 'Storage',
        nameMyanmar: 'သိုလှောင်မှု',
        description: 'Hard drives, SSDs, and storage devices',
        color: '#8B5CF6',
        icon: 'Database',
        isActive: true
    },
    {
        name: 'Networking',
        nameMyanmar: 'ကွန်ယက်',
        description: 'Routers, switches, and networking equipment',
        color: '#06B6D4',
        icon: 'Wifi',
        isActive: true
    }
];

// Sample users data
const usersData = [
    {
        firstName: 'Admin',
        lastName: 'User',
        username: 'admin',
        email: '<EMAIL>',
        password: 'admin123',
        role: 'admin',
        isActive: true,
        preferences: {
            language: 'en',
            currency: 'MMK',
            theme: 'light'
        }
    },
    {
        firstName: 'Manager',
        lastName: 'User',
        username: 'manager',
        email: '<EMAIL>',
        password: 'manager123',
        role: 'manager',
        isActive: true,
        preferences: {
            language: 'mm',
            currency: 'MMK',
            theme: 'light'
        }
    },
    {
        firstName: 'Cashier',
        lastName: 'User',
        username: 'cashier',
        email: '<EMAIL>',
        password: 'cashier123',
        role: 'cashier',
        isActive: true,
        preferences: {
            language: 'mm',
            currency: 'MMK',
            theme: 'light'
        }
    }
];

const seedUsers = async () => {
    try {
        // Clear existing users
        await User.deleteMany({});
        console.log('🗑️ Cleared existing users');

        // Create users with hashed passwords
        const users = [];
        for (const userData of usersData) {
            const salt = await bcrypt.genSalt(10);
            const hashedPassword = await bcrypt.hash(userData.password, salt);

            users.push({
                ...userData,
                password: hashedPassword
            });
        }

        await User.insertMany(users);
        console.log('👥 Users seeded successfully');
    } catch (error) {
        console.error('❌ Error seeding users:', error.message);
    }
};

const seedCategories = async () => {
    try {
        // Clear existing categories
        await Category.deleteMany({});
        console.log('🗑️ Cleared existing categories');

        // Create categories
        await Category.insertMany(categoriesData);
        console.log('📂 Categories seeded successfully');
    } catch (error) {
        console.error('❌ Error seeding categories:', error.message);
    }
};

const seedProducts = async () => {
    try {
        // Clear existing products
        await Product.deleteMany({});
        console.log('🗑️ Cleared existing products');

        // Get categories for reference
        const categories = await Category.find({});
        const categoryMap = {};
        categories.forEach(cat => {
            categoryMap[cat.name] = cat._id;
        });

        // Sample products data
        const productsData = [
            // Computers
            {
                name: 'Gaming Laptop ASUS ROG',
                nameMyanmar: 'ဂိမ်းကစားရန် လက်တော့ ASUS ROG',
                description: 'High-performance gaming laptop with RTX graphics',
                sku: 'ASUS-ROG-001',
                price: 2500000,
                cost: 2100000,
                currency: 'MMK',
                category: categoryMap['Computers'],
                inventory: { quantity: 5, unit: 'piece', minQuantity: 2 },
                isActive: true,
                tags: ['gaming', 'laptop', 'asus', 'rtx']
            },
            {
                name: 'Desktop PC Intel i7',
                nameMyanmar: 'ဒက်စ်တော့ ကွန်ပျူတာ Intel i7',
                description: 'High-performance desktop computer',
                sku: 'DESKTOP-I7-001',
                price: 1800000,
                cost: 1500000,
                currency: 'MMK',
                category: categoryMap['Computers'],
                inventory: { quantity: 8, unit: 'piece', minQuantity: 3 },
                isActive: true,
                tags: ['desktop', 'intel', 'i7']
            },
            {
                name: 'MacBook Pro 14"',
                nameMyanmar: 'MacBook Pro 14 လက်မ',
                description: 'Apple MacBook Pro with M2 chip',
                sku: 'MACBOOK-PRO-14',
                price: 3200000,
                cost: 2800000,
                currency: 'MMK',
                category: categoryMap['Computers'],
                inventory: { quantity: 3, unit: 'piece', minQuantity: 1 },
                isActive: true,
                tags: ['apple', 'macbook', 'm2', 'laptop']
            },
            // Components
            {
                name: 'Graphics Card RTX 4060',
                nameMyanmar: 'ဂရပ်ဖစ် ကတ် RTX 4060',
                description: 'NVIDIA GeForce RTX 4060 Graphics Card',
                sku: 'GPU-RTX-4060',
                price: 485000,
                cost: 420000,
                currency: 'MMK',
                category: categoryMap['Components'],
                inventory: { quantity: 12, unit: 'piece', minQuantity: 5 },
                isActive: true,
                tags: ['nvidia', 'rtx', 'graphics', 'gpu']
            },
            {
                name: 'Intel Core i5-13400F',
                nameMyanmar: 'Intel Core i5-13400F ပရိုဆက်ဆာ',
                description: 'Intel 13th Gen Core i5 Processor',
                sku: 'CPU-I5-13400F',
                price: 285000,
                cost: 240000,
                currency: 'MMK',
                category: categoryMap['Components'],
                inventory: { quantity: 15, unit: 'piece', minQuantity: 8 },
                isActive: true,
                tags: ['intel', 'cpu', 'processor', 'i5']
            },
            {
                name: 'DDR4 16GB RAM',
                nameMyanmar: 'DDR4 16GB မမ်မိုရီ',
                description: 'DDR4 16GB 3200MHz Memory',
                sku: 'RAM-DDR4-16GB',
                price: 95000,
                cost: 80000,
                currency: 'MMK',
                category: categoryMap['Components'],
                inventory: { quantity: 25, unit: 'piece', minQuantity: 10 },
                isActive: true,
                tags: ['ddr4', 'ram', 'memory', '16gb']
            },
            // Peripherals
            {
                name: 'Gaming Mouse Wireless',
                nameMyanmar: 'ဂိမ်းကစားရန် မောက်စ် ကြိုးမဲ့',
                description: 'High-precision wireless gaming mouse',
                sku: 'MOUSE-001',
                price: 80000,
                cost: 65000,
                currency: 'MMK',
                category: categoryMap['Peripherals'],
                inventory: { quantity: 30, unit: 'piece', minQuantity: 15 },
                isActive: true,
                tags: ['gaming', 'mouse', 'wireless']
            },
            {
                name: 'Mechanical Keyboard RGB',
                nameMyanmar: 'မက္ကင်နစ် ကီးဘုတ် RGB',
                description: 'RGB mechanical gaming keyboard',
                sku: 'KB-MECH-RGB',
                price: 120000,
                cost: 95000,
                currency: 'MMK',
                category: categoryMap['Peripherals'],
                inventory: { quantity: 20, unit: 'piece', minQuantity: 10 },
                isActive: true,
                tags: ['keyboard', 'mechanical', 'rgb', 'gaming']
            },
            {
                name: 'Monitor 27" 4K',
                nameMyanmar: 'မော်နီတာ 27 လက်မ 4K',
                description: '27-inch 4K UHD Monitor',
                sku: 'MON-27-4K',
                price: 800000,
                cost: 680000,
                currency: 'MMK',
                category: categoryMap['Peripherals'],
                inventory: { quantity: 8, unit: 'piece', minQuantity: 3 },
                isActive: true,
                tags: ['monitor', '4k', '27inch', 'uhd']
            },
            // Gaming
            {
                name: 'Gaming Headset Wireless',
                nameMyanmar: 'ဂိမ်းကစားရန် နားကြပ် ကြိုးမဲ့',
                description: 'Wireless gaming headset with surround sound',
                sku: 'HS-GAME-WL',
                price: 95000,
                cost: 75000,
                currency: 'MMK',
                category: categoryMap['Gaming'],
                inventory: { quantity: 18, unit: 'piece', minQuantity: 8 },
                isActive: true,
                tags: ['headset', 'gaming', 'wireless', 'surround']
            },
            {
                name: 'Gaming Chair RGB',
                nameMyanmar: 'ဂိမ်းကစားရန် ကုလားထိုင် RGB',
                description: 'Ergonomic gaming chair with RGB lighting',
                sku: 'CHAIR-GAME-RGB',
                price: 450000,
                cost: 380000,
                currency: 'MMK',
                category: categoryMap['Gaming'],
                inventory: { quantity: 6, unit: 'piece', minQuantity: 2 },
                isActive: true,
                tags: ['chair', 'gaming', 'rgb', 'ergonomic']
            },
            // Storage
            {
                name: 'SSD 1TB Samsung',
                nameMyanmar: 'SSD 1TB Samsung',
                description: 'Samsung 1TB NVMe SSD',
                sku: 'SSD-SAM-1TB',
                price: 185000,
                cost: 155000,
                currency: 'MMK',
                category: categoryMap['Storage'],
                inventory: { quantity: 22, unit: 'piece', minQuantity: 12 },
                isActive: true,
                tags: ['ssd', 'samsung', '1tb', 'nvme']
            },
            {
                name: 'External HDD 2TB',
                nameMyanmar: 'ပြင်ပ HDD 2TB',
                description: 'External hard drive 2TB USB 3.0',
                sku: 'HDD-EXT-2TB',
                price: 125000,
                cost: 105000,
                currency: 'MMK',
                category: categoryMap['Storage'],
                inventory: { quantity: 15, unit: 'piece', minQuantity: 8 },
                isActive: true,
                tags: ['hdd', 'external', '2tb', 'usb3']
            },
            // Networking
            {
                name: 'WiFi Router AC1200',
                nameMyanmar: 'WiFi ရောက်တာ AC1200',
                description: 'Dual-band WiFi router AC1200',
                sku: 'ROUTER-AC1200',
                price: 65000,
                cost: 50000,
                currency: 'MMK',
                category: categoryMap['Networking'],
                inventory: { quantity: 12, unit: 'piece', minQuantity: 6 },
                isActive: true,
                tags: ['router', 'wifi', 'ac1200', 'dualband']
            }
        ];

        await Product.insertMany(productsData);
        console.log('📦 Products seeded successfully');
    } catch (error) {
        console.error('❌ Error seeding products:', error.message);
    }
};

// Main seeding function
const seedDatabase = async () => {
    try {
        console.log('🌱 Starting database seeding...');

        await connectDB();

        // Seed in order: Users -> Categories -> Products
        await seedUsers();
        await seedCategories();
        await seedProducts();

        console.log('✅ Database seeding completed successfully!');
        console.log('\n📊 Summary:');
        console.log(`👥 Users: ${usersData.length} created`);
        console.log(`📂 Categories: ${categoriesData.length} created`);
        console.log('📦 Products: 15+ created');
        console.log('\n🔐 Default Login Credentials:');
        console.log('Admin: <EMAIL> / admin123');
        console.log('Manager: <EMAIL> / manager123');
        console.log('Cashier: <EMAIL> / cashier123');

        process.exit(0);
    } catch (error) {
        console.error('❌ Database seeding failed:', error.message);
        process.exit(1);
    }
};

// Run seeding if this file is executed directly
if (require.main === module) {
    seedDatabase();
}

module.exports = {
    seedDatabase,
    seedUsers,
    seedCategories,
    seedProducts
};
