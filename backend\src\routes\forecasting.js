const express = require('express');
const router = express.Router();
const asyncHandler = require('../middleware/asyncHandler');
const { protect, authorize } = require('../middleware/auth');
const Sale = require('../models/Sale');
const Product = require('../models/Product');
const ErrorResponse = require('../utils/errorResponse');

// @desc    Get sales forecast
// @route   GET /api/forecasting/sales
// @access  Private
router.get('/sales', protect, asyncHandler(async (req, res) => {
    const { period = 'next_week', confidence = 85 } = req.query;

    // Get historical sales data (last 90 days for better prediction)
    const historicalDays = 90;
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - historicalDays);

    const historicalSales = await Sale.find({
        createdAt: { $gte: startDate },
        status: 'completed'
    }).sort({ createdAt: 1 });

    // Calculate daily averages
    const dailyStats = {};
    historicalSales.forEach(sale => {
        const dateKey = sale.createdAt.toISOString().split('T')[0];
        if (!dailyStats[dateKey]) {
            dailyStats[dateKey] = {
                revenue: 0,
                orders: 0,
                customers: new Set(),
                items: 0
            };
        }
        dailyStats[dateKey].revenue += sale.totalAmount;
        dailyStats[dateKey].orders += 1;
        dailyStats[dateKey].customers.add(sale.customer?.name || 'walk-in');
        dailyStats[dateKey].items += sale.totalItems;
    });

    // Convert to arrays for calculation
    const dailyRevenues = Object.values(dailyStats).map(day => day.revenue);
    const dailyOrders = Object.values(dailyStats).map(day => day.orders);
    const dailyCustomers = Object.values(dailyStats).map(day => day.customers.size);

    // Simple moving average calculation
    const avgDailyRevenue = dailyRevenues.reduce((sum, val) => sum + val, 0) / dailyRevenues.length || 0;
    const avgDailyOrders = dailyOrders.reduce((sum, val) => sum + val, 0) / dailyOrders.length || 0;
    const avgDailyCustomers = dailyCustomers.reduce((sum, val) => sum + val, 0) / dailyCustomers.length || 0;

    // Calculate growth trends (last 30 days vs previous 30 days)
    const recentRevenues = dailyRevenues.slice(-30);
    const previousRevenues = dailyRevenues.slice(-60, -30);
    
    const recentAvg = recentRevenues.reduce((sum, val) => sum + val, 0) / recentRevenues.length || 0;
    const previousAvg = previousRevenues.reduce((sum, val) => sum + val, 0) / previousRevenues.length || 0;
    
    const growthRate = previousAvg > 0 ? ((recentAvg - previousAvg) / previousAvg) * 100 : 0;

    // Generate forecasts based on period
    let forecastDays, startForecast, endForecast;
    
    switch (period) {
        case 'next_week':
            forecastDays = 7;
            startForecast = new Date();
            startForecast.setDate(startForecast.getDate() + 1);
            endForecast = new Date(startForecast);
            endForecast.setDate(endForecast.getDate() + 6);
            break;
        case 'next_month':
            forecastDays = 30;
            startForecast = new Date();
            startForecast.setDate(startForecast.getDate() + 1);
            endForecast = new Date(startForecast);
            endForecast.setDate(endForecast.getDate() + 29);
            break;
        case 'next_quarter':
            forecastDays = 90;
            startForecast = new Date();
            startForecast.setDate(startForecast.getDate() + 1);
            endForecast = new Date(startForecast);
            endForecast.setDate(endForecast.getDate() + 89);
            break;
        default:
            forecastDays = 7;
            startForecast = new Date();
            startForecast.setDate(startForecast.getDate() + 1);
            endForecast = new Date(startForecast);
            endForecast.setDate(endForecast.getDate() + 6);
    }

    // Apply growth trend to predictions
    const trendMultiplier = 1 + (growthRate / 100);
    const predictedDailyRevenue = avgDailyRevenue * trendMultiplier;
    const predictedDailyOrders = avgDailyOrders * trendMultiplier;
    const predictedDailyCustomers = avgDailyCustomers * trendMultiplier;

    // Calculate confidence intervals (simple approach)
    const confidenceMultiplier = confidence / 100;
    const variance = 0.15; // 15% variance assumption
    
    const forecast = {
        period,
        startDate: startForecast.toISOString().split('T')[0],
        endDate: endForecast.toISOString().split('T')[0],
        confidence: parseInt(confidence),
        revenue: {
            predicted: Math.round(predictedDailyRevenue * forecastDays),
            min: Math.round(predictedDailyRevenue * forecastDays * (1 - variance)),
            max: Math.round(predictedDailyRevenue * forecastDays * (1 + variance)),
            growth: parseFloat(growthRate.toFixed(1))
        },
        orders: {
            predicted: Math.round(predictedDailyOrders * forecastDays),
            min: Math.round(predictedDailyOrders * forecastDays * (1 - variance)),
            max: Math.round(predictedDailyOrders * forecastDays * (1 + variance)),
            growth: parseFloat(growthRate.toFixed(1))
        },
        customers: {
            predicted: Math.round(predictedDailyCustomers * forecastDays),
            min: Math.round(predictedDailyCustomers * forecastDays * (1 - variance)),
            max: Math.round(predictedDailyCustomers * forecastDays * (1 + variance)),
            growth: parseFloat(growthRate.toFixed(1))
        },
        factors: [
            'Historical sales data',
            'Seasonal trends',
            'Growth patterns',
            'Market conditions'
        ]
    };

    res.status(200).json({
        success: true,
        data: forecast
    });
}));

// @desc    Get product demand forecast
// @route   GET /api/forecasting/products
// @access  Private
router.get('/products', protect, asyncHandler(async (req, res) => {
    const { limit = 10, period = 'next_week' } = req.query;

    // Get historical sales data for product analysis
    const historicalDays = 60;
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - historicalDays);

    const sales = await Sale.find({
        createdAt: { $gte: startDate },
        status: 'completed'
    });

    // Aggregate product sales data
    const productStats = {};
    sales.forEach(sale => {
        sale.items.forEach(item => {
            const productId = item.product.toString();
            if (!productStats[productId]) {
                productStats[productId] = {
                    productId,
                    productName: item.productName,
                    productSku: item.productSku,
                    totalQuantity: 0,
                    salesCount: 0,
                    revenue: 0,
                    dailySales: {}
                };
            }

            const dateKey = sale.createdAt.toISOString().split('T')[0];
            if (!productStats[productId].dailySales[dateKey]) {
                productStats[productId].dailySales[dateKey] = 0;
            }

            productStats[productId].totalQuantity += item.quantity;
            productStats[productId].salesCount += 1;
            productStats[productId].revenue += item.totalPrice;
            productStats[productId].dailySales[dateKey] += item.quantity;
        });
    });

    // Calculate forecasts for each product
    const productForecasts = Object.values(productStats)
        .map(product => {
            const dailySalesArray = Object.values(product.dailySales);
            const avgDailySales = dailySalesArray.reduce((sum, val) => sum + val, 0) / historicalDays;
            
            // Simple trend calculation
            const recentSales = dailySalesArray.slice(-14); // Last 2 weeks
            const previousSales = dailySalesArray.slice(-28, -14); // Previous 2 weeks
            
            const recentAvg = recentSales.reduce((sum, val) => sum + val, 0) / recentSales.length || 0;
            const previousAvg = previousSales.reduce((sum, val) => sum + val, 0) / previousSales.length || 0;
            
            const trend = previousAvg > 0 ? ((recentAvg - previousAvg) / previousAvg) * 100 : 0;
            
            let forecastDays = 7;
            if (period === 'next_month') forecastDays = 30;
            if (period === 'next_quarter') forecastDays = 90;

            const trendMultiplier = 1 + (trend / 100);
            const predictedQuantity = Math.round(avgDailySales * trendMultiplier * forecastDays);

            return {
                productId: product.productId,
                productName: product.productName,
                productSku: product.productSku,
                currentStock: 0, // Will be updated from Product model
                predictedDemand: predictedQuantity,
                confidence: Math.min(95, Math.max(60, 85 + (product.salesCount / 10))), // Confidence based on sales frequency
                trend: trend > 5 ? 'increasing' : trend < -5 ? 'decreasing' : 'stable',
                trendPercentage: parseFloat(trend.toFixed(1)),
                category: 'General' // Will be updated from Product model
            };
        })
        .sort((a, b) => b.predictedDemand - a.predictedDemand)
        .slice(0, parseInt(limit));

    // Get current stock levels from Product model
    const productIds = productForecasts.map(pf => pf.productId);
    const products = await Product.find({ _id: { $in: productIds } })
        .populate('category', 'name');

    // Update forecasts with current stock and category info
    productForecasts.forEach(forecast => {
        const product = products.find(p => p._id.toString() === forecast.productId);
        if (product) {
            forecast.currentStock = product.inventory?.quantity || 0;
            forecast.category = product.category?.name || 'General';
            forecast.reorderSuggestion = forecast.currentStock < forecast.predictedDemand;
        }
    });

    res.status(200).json({
        success: true,
        data: productForecasts
    });
}));

// @desc    Get market trends analysis
// @route   GET /api/forecasting/trends
// @access  Private
router.get('/trends', protect, asyncHandler(async (req, res) => {
    const { period = '90d' } = req.query;

    const days = parseInt(period.replace('d', '')) || 90;
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - days);

    const sales = await Sale.find({
        createdAt: { $gte: startDate },
        status: 'completed'
    }).sort({ createdAt: 1 });

    // Analyze trends by week
    const weeklyData = {};
    sales.forEach(sale => {
        const weekStart = new Date(sale.createdAt);
        weekStart.setDate(weekStart.getDate() - weekStart.getDay());
        const weekKey = weekStart.toISOString().split('T')[0];

        if (!weeklyData[weekKey]) {
            weeklyData[weekKey] = {
                week: weekKey,
                revenue: 0,
                orders: 0,
                customers: new Set(),
                avgOrderValue: 0
            };
        }

        weeklyData[weekKey].revenue += sale.totalAmount;
        weeklyData[weekKey].orders += 1;
        weeklyData[weekKey].customers.add(sale.customer?.name || 'walk-in');
    });

    // Calculate average order values
    Object.values(weeklyData).forEach(week => {
        week.avgOrderValue = week.orders > 0 ? week.revenue / week.orders : 0;
        week.customers = week.customers.size;
    });

    const trends = Object.values(weeklyData).sort((a, b) => a.week.localeCompare(b.week));

    // Calculate overall trends
    const recentWeeks = trends.slice(-4); // Last 4 weeks
    const previousWeeks = trends.slice(-8, -4); // Previous 4 weeks

    const recentAvgRevenue = recentWeeks.reduce((sum, week) => sum + week.revenue, 0) / recentWeeks.length || 0;
    const previousAvgRevenue = previousWeeks.reduce((sum, week) => sum + week.revenue, 0) / previousWeeks.length || 0;

    const revenueTrend = previousAvgRevenue > 0 ? ((recentAvgRevenue - previousAvgRevenue) / previousAvgRevenue) * 100 : 0;

    res.status(200).json({
        success: true,
        data: {
            trends,
            summary: {
                revenueTrend: parseFloat(revenueTrend.toFixed(1)),
                trendDirection: revenueTrend > 5 ? 'increasing' : revenueTrend < -5 ? 'decreasing' : 'stable',
                totalWeeks: trends.length,
                avgWeeklyRevenue: trends.reduce((sum, week) => sum + week.revenue, 0) / trends.length || 0
            }
        }
    });
}));

module.exports = router;
