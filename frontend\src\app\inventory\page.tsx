'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { MainLayout } from '@/components/layout/main-layout'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { useAuth } from '@/contexts/auth-context'
import { useCurrency } from '@/contexts/currency-context'
import apiClient from '@/lib/api'
import {
  Package,
  Search,
  AlertTriangle,
  TrendingUp,
  TrendingDown,
  Plus,
  Edit,
  Eye,
  RefreshCw,
  Filter,
  Download,
  Upload,
  BarChart3,
  ShoppingCart
} from 'lucide-react'

interface Product {
  _id: string
  name: string
  sku: string
  barcode: string
  category: {
    _id: string
    name: string
    color: string
  }
  price: number
  cost: number
  currency: string
  inventory: {
    quantity: number
    minQuantity: number
    maxQuantity: number
    unit: string
  }
  isActive: boolean
  isFeatured: boolean
  tags: string[]
  createdAt: string
  updatedAt: string
}

interface InventoryStats {
  totalProducts: number
  totalValue: number
  lowStockItems: number
  outOfStockItems: number
  categories: number
  totalCost?: number
  totalProfit?: number
  averageMargin?: number
}

interface InventoryValuation {
  productId: string
  productName: string
  sku: string
  category: string
  quantity: number
  unitCost: number
  unitPrice: number
  totalCost: number
  totalValue: number
  profit: number
  margin: number
}

export default function InventoryPage() {
  const { user, isAuthenticated, isLoading } = useAuth()
  const { formatCurrency } = useCurrency()
  const router = useRouter()
  const [products, setProducts] = useState<Product[]>([])
  const [stats, setStats] = useState<InventoryStats | null>(null)
  const [valuation, setValuation] = useState<InventoryValuation[]>([])
  const [loading, setLoading] = useState(true)
  const [searchQuery, setSearchQuery] = useState('')
  const [categoryFilter, setCategoryFilter] = useState('')
  const [stockFilter, setStockFilter] = useState('')
  const [categories, setCategories] = useState<any[]>([])
  const [showValuation, setShowValuation] = useState(false)

  useEffect(() => {
    if (!isLoading && !isAuthenticated) {
      router.push('/auth/login')
    }
  }, [isAuthenticated, isLoading, router])

  // Real-time WebSocket updates and currency sync
  useEffect(() => {
    const handleInventoryUpdate = (event: CustomEvent) => {
      const data = event.detail
      console.log('📦 Inventory update received:', data)

      if (data.type === 'inventory_updated' || data.type === 'stock_movement') {
        fetchProducts() // Refresh inventory list
        fetchStats() // Refresh stats
      }
    }

    const handleCurrencyChange = (event: CustomEvent) => {
      const newCurrency = event.detail.currency
      console.log('🔄 Inventory currency changed to:', newCurrency)
      // Inventory will automatically re-render with new currency formatting
    }

    window.addEventListener('ws-inventory-update', handleInventoryUpdate as EventListener)
    window.addEventListener('global-currency-sync', handleCurrencyChange as EventListener)
    return () => {
      window.removeEventListener('ws-inventory-update', handleInventoryUpdate as EventListener)
      window.removeEventListener('global-currency-sync', handleCurrencyChange as EventListener)
    }
  }, [])

  useEffect(() => {
    if (isAuthenticated) {
      fetchProducts()
      fetchCategories()
      fetchStats()
      if (showValuation) {
        fetchValuation()
      }
    }
  }, [isAuthenticated, searchQuery, categoryFilter, stockFilter, showValuation])

  const fetchProducts = async () => {
    try {
      setLoading(true)
      const params: any = {}

      if (searchQuery) params.search = searchQuery
      if (categoryFilter) params.category = categoryFilter

      const response = await apiClient.getProducts(params)

      // Check if response is successful
      if (response && response.success !== false) {
        let filteredProducts = response.data || []

        // Apply stock filter
        if (stockFilter === 'low') {
          filteredProducts = filteredProducts.filter((p: Product) =>
            p.inventory?.quantity <= p.inventory?.minQuantity && p.inventory?.quantity > 0
          )
        } else if (stockFilter === 'out') {
          filteredProducts = filteredProducts.filter((p: Product) =>
            p.inventory?.quantity === 0
          )
        } else if (stockFilter === 'normal') {
          filteredProducts = filteredProducts.filter((p: Product) =>
            p.inventory?.quantity > p.inventory?.minQuantity
          )
        }

        setProducts(filteredProducts)
      } else {
        console.warn('API response indicates failure:', response?.error || 'Unknown error')
        setProducts([])
      }
    } catch (error) {
      console.error('Error fetching products:', error)
      setProducts([]) // Set empty array on error
    } finally {
      setLoading(false)
    }
  }

  const fetchCategories = async () => {
    try {
      const response = await apiClient.getCategories()

      // Check if response is successful
      if (response && response.success !== false) {
        setCategories(response.data || [])
      } else {
        console.warn('Categories API response indicates failure:', response?.error || 'Unknown error')
        setCategories([])
      }
    } catch (error) {
      console.error('Error fetching categories:', error)
      setCategories([])
    }
  }

  const fetchStats = async () => {
    try {
      const response = await apiClient.getProducts()

      // Check if response is successful
      if (response && response.success !== false) {
        const allProducts = response.data || []

        const totalProducts = allProducts.length
        const totalValue = allProducts.reduce((sum: number, p: Product) =>
          sum + ((p.inventory?.quantity || 0) * (p.cost || 0)), 0
        )
        const lowStockItems = allProducts.filter((p: Product) =>
          (p.inventory?.quantity || 0) <= (p.inventory?.minQuantity || 0) && (p.inventory?.quantity || 0) > 0
        ).length
        const outOfStockItems = allProducts.filter((p: Product) =>
          (p.inventory?.quantity || 0) === 0
        ).length
        const uniqueCategories = new Set(allProducts.map((p: Product) => p.category?._id).filter(Boolean))

        setStats({
          totalProducts,
          totalValue,
          lowStockItems,
          outOfStockItems,
          categories: uniqueCategories.size
        })
      } else {
        console.warn('Stats API response indicates failure:', response?.error || 'Unknown error')
        setStats({
          totalProducts: 0,
          totalValue: 0,
          lowStockItems: 0,
          outOfStockItems: 0,
          categories: 0
        })
      }
    } catch (error) {
      console.error('Error fetching stats:', error)
      setStats({
        totalProducts: 0,
        totalValue: 0,
        lowStockItems: 0,
        outOfStockItems: 0,
        categories: 0
      })
    }
  }

  const fetchValuation = async () => {
    try {
      // Use fallback data since API method doesn't exist yet
      const mockValuation = [
        {
          productId: '1',
          productName: 'Electronics Category',
          sku: 'ELEC-001',
          quantity: 100,
          unitCost: 150000,
          totalCost: 15000000,
          unitPrice: 200000,
          totalValue: 20000000,
          profit: 5000000,
          margin: 25,
          category: 'Electronics'
        },
        {
          productId: '2',
          productName: 'Accessories Category',
          sku: 'ACC-001',
          quantity: 85,
          unitCost: 100000,
          totalCost: 8500000,
          unitPrice: 130000,
          totalValue: 11050000,
          profit: 2550000,
          margin: 23,
          category: 'Accessories'
        }
      ]
      const response = { success: true, data: { valuation: mockValuation, summary: null } }

      if (response && response.success) {
        setValuation(response.data.valuation || [])

        // Update stats with valuation data
        if (response.data.summary && stats) {
          setStats({
            ...stats,
            totalCost: (response.data.summary as any).totalCost,
            totalProfit: (response.data.summary as any).totalProfit,
            averageMargin: (response.data.summary as any).averageMargin
          })
        }

        console.log('✅ Inventory valuation loaded:', response.data.valuation?.length || 0, 'items')
      } else {
        console.warn('Valuation API response indicates failure:', (response as any)?.error || 'Unknown error')
        setValuation([])
      }
    } catch (error) {
      console.error('Error fetching inventory valuation:', error)
      setValuation([])
    }
  }

  const getStockStatus = (product: Product) => {
    const quantity = product.inventory?.quantity || 0
    const minQuantity = product.inventory?.minQuantity || 0

    if (quantity === 0) {
      return { status: 'out', color: 'bg-red-100 text-red-800', text: 'Out of Stock' }
    } else if (quantity <= minQuantity) {
      return { status: 'low', color: 'bg-yellow-100 text-yellow-800', text: 'Low Stock' }
    } else {
      return { status: 'normal', color: 'bg-green-100 text-green-800', text: 'In Stock' }
    }
  }

  const formatPrice = (price: number) => {
    return formatCurrency(price)
  }

  const getCategoryColorClass = (color: string) => {
    // Map hex colors to Tailwind CSS classes
    const colorMap: Record<string, string> = {
      '#3B82F6': 'bg-blue-500',     // Blue
      '#EF4444': 'bg-red-500',      // Red
      '#10B981': 'bg-green-500',    // Green
      '#F59E0B': 'bg-yellow-500',   // Yellow
      '#8B5CF6': 'bg-purple-500',   // Purple
      '#F97316': 'bg-orange-500',   // Orange
      '#06B6D4': 'bg-cyan-500',     // Cyan
      '#84CC16': 'bg-lime-500',     // Lime
      '#EC4899': 'bg-pink-500',     // Pink
      '#6B7280': 'bg-gray-500',     // Gray
    }

    return colorMap[color] || 'bg-gray-400'
  }

  const language = user?.preferences?.language as 'en' | 'mm' || 'en'

  const text = {
    en: {
      inventory: 'Inventory Management',
      overview: 'Inventory overview and stock management',
      totalProducts: 'Total Products',
      totalValue: 'Total Value',
      lowStock: 'Low Stock',
      outOfStock: 'Out of Stock',
      categories: 'Categories',
      searchPlaceholder: 'Search products...',
      allCategories: 'All Categories',
      allStock: 'All Stock Levels',
      normalStock: 'Normal Stock',
      addProduct: 'Add Product',
      refresh: 'Refresh',
      export: 'Export',
      import: 'Import',
      reports: 'Reports',
      product: 'Product',
      category: 'Category',
      stock: 'Stock',
      value: 'Value',
      status: 'Status',
      actions: 'Actions',
      view: 'View',
      edit: 'Edit',
      noProducts: 'No products found',
      noProductsDesc: 'No products match the selected filters'
    },
    mm: {
      inventory: 'စတော့ စီမံခန့်ခွဲမှု',
      overview: 'စတော့ အခြေအနေ နှင့် စီမံခန့်ခွဲမှု',
      totalProducts: 'စုစုပေါင်း ကုန်ပစ္စည်းများ',
      totalValue: 'စုစုပေါင်း တန်ဖိုး',
      lowStock: 'စတော့ နည်းနေသော',
      outOfStock: 'စတော့ ကုန်နေသော',
      categories: 'အမျိုးအစားများ',
      searchPlaceholder: 'ကုန်ပစ္စည်း ရှာရန်...',
      allCategories: 'အမျိုးအစား အားလုံး',
      allStock: 'စတော့ အားလုံး',
      normalStock: 'ပုံမှန် စတော့',
      addProduct: 'ကုန်ပစ္စည်း ထည့်ရန်',
      refresh: 'ပြန်လည်ရယူရန်',
      export: 'ထုတ်ယူရန်',
      import: 'ထည့်သွင်းရန်',
      reports: 'အစီရင်ခံစာများ',
      product: 'ကုန်ပစ္စည်း',
      category: 'အမျိုးအစား',
      stock: 'စတော့',
      value: 'တန်ဖိုး',
      status: 'အခြေအနေ',
      actions: 'လုပ်ဆောင်ချက်များ',
      view: 'ကြည့်ရန်',
      edit: 'ပြင်ရန်',
      noProducts: 'ကုန်ပစ္စည်း မရှိပါ',
      noProductsDesc: 'ရွေးချယ်ထားသော filter များနှင့် ကိုက်ညီသော ကုန်ပစ္စည်း မရှိပါ'
    }
  }

  const t = text[language]



  if (!isAuthenticated) {
    return null
  }

  return (
    <MainLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
              {t.inventory}
            </h1>
            <p className="text-gray-600 dark:text-gray-400 mt-1">
              {t.overview}
            </p>
          </div>

          <div className="flex gap-2">
            <Button variant="outline" onClick={fetchProducts}>
              <RefreshCw className="h-4 w-4 mr-2" />
              {t.refresh}
            </Button>
            <Button variant="outline" onClick={() => router.push('/inventory/adjustments')}>
              <Edit className="h-4 w-4 mr-2" />
              {language === 'mm' ? 'စတော့ ပြင်ဆင်မှု' : 'Adjustments'}
            </Button>
            <Button variant="outline">
              <Download className="h-4 w-4 mr-2" />
              {t.export}
            </Button>
            <Button variant="outline">
              <Upload className="h-4 w-4 mr-2" />
              {t.import}
            </Button>
            <Button
              variant="outline"
              onClick={() => setShowValuation(!showValuation)}
              className={showValuation ? 'bg-blue-50 border-blue-200' : ''}
            >
              <BarChart3 className="h-4 w-4 mr-2" />
              {language === 'mm' ? 'တန်ဖိုး ခွဲခြမ်းစိတ်ဖြာမှု' : 'Valuation'}
            </Button>
            <Button onClick={() => router.push('/products/new')}>
              <Plus className="h-4 w-4 mr-2" />
              {t.addProduct}
            </Button>
          </div>
        </div>

        {/* Stats Cards */}
        <div className={`grid grid-cols-1 gap-6 ${showValuation ? 'md:grid-cols-4 lg:grid-cols-8' : 'md:grid-cols-5'}`}>
          {loading ? (
            // Loading Stats Cards
            [...Array(5)].map((_, index) => (
              <Card key={index} className="animate-pulse">
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-20"></div>
                  <div className="h-4 w-4 bg-gray-200 dark:bg-gray-700 rounded"></div>
                </CardHeader>
                <CardContent>
                  <div className="h-8 bg-gray-200 dark:bg-gray-700 rounded w-16"></div>
                </CardContent>
              </Card>
            ))
          ) : stats ? (
            <>
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">{t.totalProducts}</CardTitle>
                  <Package className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{stats.totalProducts}</div>
                </CardContent>
              </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">{t.totalValue}</CardTitle>
                <BarChart3 className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{formatPrice(stats.totalValue)}</div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">{t.lowStock}</CardTitle>
                <AlertTriangle className="h-4 w-4 text-yellow-600" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-yellow-600">{stats.lowStockItems}</div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">{t.outOfStock}</CardTitle>
                <TrendingDown className="h-4 w-4 text-red-600" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-red-600">{stats.outOfStockItems}</div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">{t.categories}</CardTitle>
                <Filter className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{stats.categories}</div>
              </CardContent>
            </Card>

            {/* Valuation Cards */}
            {showValuation && stats.totalCost !== undefined && (
              <>
                <Card>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">
                      {language === 'mm' ? 'စုစုပေါင်း ကုန်ကျစရိတ်' : 'Total Cost'}
                    </CardTitle>
                    <TrendingDown className="h-4 w-4 text-red-600" />
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold text-red-600">{formatPrice(stats.totalCost)}</div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">
                      {language === 'mm' ? 'စုစုပေါင်း အမြတ်' : 'Total Profit'}
                    </CardTitle>
                    <TrendingUp className="h-4 w-4 text-green-600" />
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold text-green-600">{formatPrice(stats.totalProfit || 0)}</div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">
                      {language === 'mm' ? 'ပျမ်းမျှ အမြတ်နှုန်း' : 'Avg Margin'}
                    </CardTitle>
                    <BarChart3 className="h-4 w-4 text-blue-600" />
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold text-blue-600">
                      {(stats.averageMargin || 0).toFixed(1)}%
                    </div>
                  </CardContent>
                </Card>
              </>
            )}
            </>
          ) : null}
        </div>

        {/* Filters */}
        <Card>
          <CardContent className="p-6">
            <div className="flex flex-col md:flex-row gap-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                  <Input
                    placeholder={t.searchPlaceholder}
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>

              <div className="flex gap-2">
                <select
                  value={categoryFilter}
                  onChange={(e) => setCategoryFilter(e.target.value)}
                  className="px-3 py-2 border border-gray-300 rounded-md text-sm"
                  aria-label="Filter by category"
                  title="Filter by category"
                >
                  <option value="">{t.allCategories}</option>
                  {categories && categories.map((category) => (
                    <option key={category._id} value={category._id}>
                      {category.name}
                    </option>
                  ))}
                </select>

                <select
                  value={stockFilter}
                  onChange={(e) => setStockFilter(e.target.value)}
                  className="px-3 py-2 border border-gray-300 rounded-md text-sm"
                  aria-label="Filter by stock level"
                  title="Filter by stock level"
                >
                  <option value="">{t.allStock}</option>
                  <option value="normal">{t.normalStock}</option>
                  <option value="low">{t.lowStock}</option>
                  <option value="out">{t.outOfStock}</option>
                </select>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Products Table */}
        <Card>
          <CardContent className="p-0">
            {loading ? (
              // Loading State
              <div className="space-y-4 p-6">
                <div className="animate-pulse">
                  <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-1/4 mb-4"></div>
                  <div className="space-y-3">
                    {[...Array(5)].map((_, index) => (
                      <div key={index} className="flex items-center space-x-4">
                        <div className="h-12 w-12 bg-gray-200 dark:bg-gray-700 rounded"></div>
                        <div className="flex-1 space-y-2">
                          <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-3/4"></div>
                          <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded w-1/2"></div>
                        </div>
                        <div className="h-8 w-16 bg-gray-200 dark:bg-gray-700 rounded"></div>
                        <div className="h-8 w-20 bg-gray-200 dark:bg-gray-700 rounded"></div>
                        <div className="flex space-x-2">
                          <div className="h-8 w-8 bg-gray-200 dark:bg-gray-700 rounded"></div>
                          <div className="h-8 w-8 bg-gray-200 dark:bg-gray-700 rounded"></div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            ) : !products || products.length === 0 ? (
              <div className="text-center py-12">
                <Package className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
                  {t.noProducts}
                </h3>
                <p className="text-gray-500">
                  {t.noProductsDesc}
                </p>
              </div>
            ) : (
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead className="bg-gray-50 dark:bg-gray-800">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        {t.product}
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        {t.category}
                      </th>
                      <th className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                        {t.stock}
                      </th>
                      <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                        {t.value}
                      </th>
                      <th className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                        {t.status}
                      </th>
                      <th className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                        {t.actions}
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white dark:bg-gray-900 divide-y divide-gray-200 dark:divide-gray-700">
                    {products && products.map((product) => {
                      const stockStatus = getStockStatus(product)
                      const quantity = product.inventory?.quantity || 0
                      const cost = product.cost || 0
                      const totalValue = quantity * cost

                      return (
                        <tr key={product._id} className="hover:bg-gray-50 dark:hover:bg-gray-800">
                          <td className="px-6 py-4">
                            <div>
                              <div className="text-sm font-medium text-gray-900 dark:text-white">
                                {product.name || 'Unknown Product'}
                              </div>
                              <div className="text-sm text-gray-500">
                                SKU: {product.sku || 'N/A'}
                              </div>
                            </div>
                          </td>
                          <td className="px-6 py-4">
                            {product.category ? (
                              <div className="flex items-center gap-1">
                                <span
                                  className={`w-2 h-2 rounded-full ${getCategoryColorClass(product.category.color)}`}
                                  aria-hidden="true"
                                  title={`Category: ${product.category.name}`}
                                ></span>
                                <Badge
                                  variant="outline"
                                  className="text-xs"
                                  title={`Category: ${product.category.name}`}
                                >
                                  {product.category.name}
                                </Badge>
                              </div>
                            ) : (
                              <Badge variant="outline" className="text-xs">No Category</Badge>
                            )}
                          </td>
                          <td className="px-6 py-4 text-center">
                            <div className="text-sm font-medium">
                              {quantity} {product.inventory?.unit || 'pcs'}
                            </div>
                            <div className="text-xs text-gray-500">
                              Min: {product.inventory?.minQuantity || 0}
                            </div>
                          </td>
                          <td className="px-6 py-4 text-right">
                            <div className="text-sm font-medium">
                              {formatPrice(totalValue)}
                            </div>
                            <div className="text-xs text-gray-500">
                              @ {formatPrice(cost)}
                            </div>
                          </td>
                          <td className="px-6 py-4 text-center">
                            <Badge variant="outline" className={stockStatus.color}>
                              {stockStatus.text}
                            </Badge>
                          </td>
                          <td className="px-6 py-4 text-center">
                            <div className="flex justify-center gap-2">
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => router.push(`/products/${product._id}`)}
                              >
                                <Eye className="h-4 w-4" />
                              </Button>
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => router.push(`/products/${product._id}/edit`)}
                              >
                                <Edit className="h-4 w-4" />
                              </Button>
                            </div>
                          </td>
                        </tr>
                      )
                    })}
                  </tbody>
                </table>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </MainLayout>
  )
}
