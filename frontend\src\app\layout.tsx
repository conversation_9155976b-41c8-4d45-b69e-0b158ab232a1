import type { Metada<PERSON> } from "next";
import "./globals.css";
import { AuthProvider } from "@/contexts/auth-context";
import { SettingsProvider } from "@/contexts/settings-context";
import { ThemeProvider } from "@/contexts/theme-context";
import { <PERSON>urrencyProvider } from "@/contexts/currency-context";
import { NotificationProvider } from "@/contexts/notification-context";

export const metadata: Metadata = {
  title: "BitsTech POS System",
  description: "Modern Point-of-Sale Solution for Myanmar Businesses",
  icons: {
    icon: '/favicon.ico',
    apple: '/icons/apple-touch-icon.png',
  },
  manifest: '/manifest.json',
};

export const viewport = {
  width: 'device-width',
  initialScale: 1.0,
  maximumScale: 1.0,
  userScalable: false,
  shrinkToFit: false,
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body
        className="font-sans antialiased"
        suppressHydrationWarning
      >
        <CurrencyProvider>
          <ThemeProvider>
            <SettingsProvider>
              <AuthProvider>
                <NotificationProvider>
                  {children}
                </NotificationProvider>
              </AuthProvider>
            </SettingsProvider>
          </ThemeProvider>
        </CurrencyProvider>
      </body>
    </html>
  );
}
